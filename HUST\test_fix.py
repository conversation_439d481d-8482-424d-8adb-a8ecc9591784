"""
测试修复后的Excel写入功能
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.excel.writer import ExcelWriter

def test_update_specific_columns():
    """测试新的更新指定列功能"""
    print("测试新的更新指定列功能...")
    
    # 测试数据
    test_data = [
        {'名称': 'MD_1_测试项目1', '类型': 300},
        {'名称': 'MD_2_测试项目2', '类型': 300},
        {'名称': 'MD_3_测试项目3', '类型': 300}
    ]
    
    # 列映射
    column_mapping = {
        '名称': 2,  # B列
        '类型': 5   # E列
    }
    
    try:
        # 创建写入器
        writer = ExcelWriter('电源明细表手动转HUST.xlsx')
        
        # 加载工作簿
        if not writer.load_or_create_workbook():
            print("❌ 无法加载工作簿")
            return False
        
        # 检查工作表
        if '电站表' not in writer.workbook.sheetnames:
            print("❌ 目标工作表'电站表'不存在")
            print(f"可用工作表: {writer.workbook.sheetnames}")
            return False
        
        print("✅ 工作簿加载成功")
        print(f"✅ 找到目标工作表'电站表'")
        
        # 使用新方法更新指定列
        success = writer.update_specific_columns(
            sheet_name='电站表',
            data=test_data,
            column_mapping=column_mapping,
            start_row=2
        )
        
        if success:
            print("✅ 数据更新成功")
            
            # 保存文件
            if writer.save():
                print("✅ 文件保存成功")
                writer.close()
                return True
            else:
                print("❌ 文件保存失败")
                return False
        else:
            print("❌ 数据更新失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("测试Excel写入修复")
    print("=" * 50)
    
    result = test_update_specific_columns()
    
    if result:
        print("\n🎉 测试成功！修复有效。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
