import pandas as pd
from datetime import datetime

def unit_get_cols_one_row(data, cols_name, type_val, station_id=56):
    """
    添加新列并设置默认值

    Args:
        data: 数据DataFrame
        cols_name: 列名列表
        type_val: 类型值
        station_id: 电站ID，默认为56
    """
    # 处理中调水电类型
    if type_val == '中调水电' or type_val == 'SD_KT':
        # 设置电站ID（从电站表中获取，或使用传入的值）
        data['电站ID'] = station_id

        # 根据转换逻辑文档设置各列的值
        for col_name in cols_name:
            if col_name == '动态投资':
                data[col_name] = 10000
            elif col_name == '燃料单价':
                data[col_name] = 0
            elif col_name == '燃料单耗':
                data[col_name] = 0
            elif col_name == '检修天数':
                data[col_name] = 30
            elif col_name == '特性ID':
                data[col_name] = 1
            elif col_name == '有效性':
                data[col_name] = 1
            elif col_name == '台数':
                data[col_name] = 1
            elif col_name == '上网电价' or col_name == '汛期电价':
                data[col_name] = 0.3296
            elif col_name == '技术出力':
                data[col_name] = 0.5
            elif col_name == '爬坡率':
                data[col_name] = 0.75
            elif col_name == '运维费率':
                data[col_name] = 0.01
            elif col_name == '运行费':
                data[col_name] = 0.001
            elif col_name == '强迫停运':
                data[col_name] = 0.05
            elif col_name in ['类型', '储能库容', '退役年月', '退役进度', '变电投资', '功频系数', '惯性常数']:
                data[col_name] = 0
            # 投产进度在后面单独处理

    # 处理投产年月和投产进度逻辑
    cutoff_date = datetime(2025, 6, 12)

    # 处理投产年月列
    if '投产年月' in data.columns:
        # 将投产时间转换为datetime类型
        data['投产年月_temp'] = pd.to_datetime(data['投产年月'], errors='coerce')

        # 初始化投产进度
        data['投产进度'] = 0

        for i in range(len(data)):
            production_date = data.loc[i, '投产年月_temp']

            if pd.isna(production_date):
                # 投产时间为空或无效
                data.loc[i, '投产年月'] = "0"
                data.loc[i, '投产进度'] = 0
            elif production_date < cutoff_date:
                # 投产时间早于2025/6/12
                data.loc[i, '投产年月'] = "0"
                data.loc[i, '投产进度'] = 0
            else:
                # 投产时间晚于2025/6/12
                data.loc[i, '投产年月'] = production_date.strftime('%Y%m')
                data.loc[i, '投产进度'] = 101

        # 删除临时列
        data.drop('投产年月_temp', axis=1, inplace=True)

    # 确保返回指定的列顺序
    cols_name = ['名称', '有效性', '电站ID', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                 '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                 '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                 '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']

    return data[cols_name]
