"""
调试数据结构脚本
检查各个Excel文件的数据结构和内容
"""
import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.excel.reader import ExcelReader

def analyze_excel_file(file_path: str, sheet_name: str = None):
    """分析Excel文件结构"""
    print(f"\n=== 分析文件: {file_path} ===")
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        reader = ExcelReader(file_path)
        
        if not reader.load_workbook():
            print("❌ 无法加载工作簿")
            return
        
        # 显示所有工作表
        sheet_names = reader.get_sheet_names()
        print(f"工作表列表: {sheet_names}")
        
        # 如果指定了工作表，分析该工作表
        if sheet_name and sheet_name in sheet_names:
            print(f"\n--- 分析工作表: {sheet_name} ---")
            
            data_list = reader.read_sheet_data(sheet_name)
            if data_list:
                df = pd.DataFrame(data_list)
                print(f"数据形状: {df.shape}")
                print(f"列名: {list(df.columns)}")
                
                print("\n前3行数据:")
                for idx, row in df.head(3).iterrows():
                    print(f"第{idx+1}行: {dict(row)}")
                
                # 检查特定列的数据类型和示例值
                for col in df.columns:
                    non_null_values = df[col].dropna()
                    if len(non_null_values) > 0:
                        print(f"\n列 '{col}':")
                        print(f"  数据类型: {df[col].dtype}")
                        print(f"  非空值数量: {len(non_null_values)}")
                        print(f"  示例值: {list(non_null_values.head(3))}")
            else:
                print("❌ 工作表中没有数据")
        
        # 如果没有指定工作表，分析所有工作表的基本信息
        elif not sheet_name:
            for sheet in sheet_names:
                print(f"\n--- 工作表: {sheet} ---")
                try:
                    data_list = reader.read_sheet_data(sheet)
                    if data_list:
                        df = pd.DataFrame(data_list)
                        print(f"  数据形状: {df.shape}")
                        print(f"  列名: {list(df.columns)[:10]}...")  # 只显示前10列
                    else:
                        print("  无数据")
                except Exception as e:
                    print(f"  读取失败: {str(e)}")
        
        reader.close()
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("HUST数据结构调试工具")
    print("=" * 50)
    
    # 文件路径
    source_file = "01 电源明细表-模板.xlsx"
    target_file = "电源明细表手动转HUST.xlsx"
    
    print("请选择要分析的内容:")
    print("1. 源文件 - 所有工作表概览")
    print("2. 源文件 - Sheet1详细分析")
    print("3. 目标文件 - 所有工作表概览")
    print("4. 目标文件 - 电站表详细分析")
    print("5. 目标文件 - 机组表详细分析")
    print("6. 目标文件 - 特性表详细分析")
    print("7. 全部分析")
    print("8. 退出")
    
    choice = input("\n请输入选择 (1-8): ").strip()
    
    if choice == '1':
        analyze_excel_file(source_file)
    elif choice == '2':
        analyze_excel_file(source_file, 'Sheet1')
    elif choice == '3':
        analyze_excel_file(target_file)
    elif choice == '4':
        analyze_excel_file(target_file, '电站表')
    elif choice == '5':
        analyze_excel_file(target_file, '机组表')
    elif choice == '6':
        analyze_excel_file(target_file, '特性表')
    elif choice == '7':
        # 全部分析
        analyze_excel_file(source_file, 'Sheet1')
        analyze_excel_file(target_file, '电站表')
        analyze_excel_file(target_file, '机组表')
        analyze_excel_file(target_file, '特性表')
    elif choice == '8':
        print("退出")
        return
    else:
        print("❌ 无效选择")
        return
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
