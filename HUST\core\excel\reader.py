"""
Excel读取模块
"""
import pandas as pd
import json
import logging
from typing import Dict, List, Any, Optional
from openpyxl import load_workbook

logger = logging.getLogger(__name__)

class ExcelReader:
    """Excel文件读取器"""
    
    def __init__(self, file_path: str):
        """
        初始化Excel读取器
        
        Args:
            file_path (str): Excel文件路径
        """
        self.file_path = file_path
        self.workbook = None
        self.sheet_names = []
        
    def load_workbook(self) -> bool:
        """
        加载Excel工作簿
        
        Returns:
            bool: 是否加载成功
        """
        try:
            self.workbook = load_workbook(self.file_path, read_only=True)
            self.sheet_names = self.workbook.sheetnames
            logger.info(f"成功加载Excel文件: {self.file_path}")
            logger.info(f"工作表: {self.sheet_names}")
            return True
        except Exception as e:
            logger.error(f"加载Excel文件失败: {str(e)}")
            return False
    
    def get_sheet_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有工作表的基本信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 工作表信息字典
        """
        if not self.workbook:
            if not self.load_workbook():
                return {}
        
        sheet_info = {}
        for sheet_name in self.sheet_names:
            try:
                # 使用pandas读取前几行来获取列信息
                df = pd.read_excel(self.file_path, sheet_name=sheet_name, nrows=5)
                
                # 获取完整数据的行数
                df_full = pd.read_excel(self.file_path, sheet_name=sheet_name)
                
                sheet_info[sheet_name] = {
                    'columns': list(df.columns),
                    'column_count': len(df.columns),
                    'row_count': len(df_full),
                    'sample_data': df.head(3).to_dict('records')
                }
                
            except Exception as e:
                logger.warning(f"读取工作表 {sheet_name} 信息失败: {str(e)}")
                sheet_info[sheet_name] = {
                    'columns': [],
                    'column_count': 0,
                    'row_count': 0,
                    'sample_data': [],
                    'error': str(e)
                }
        
        return sheet_info
    
    def read_sheet_data(self, sheet_name: str, start_row: int = 0, 
                       end_row: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        读取指定工作表的数据
        
        Args:
            sheet_name (str): 工作表名称
            start_row (int): 起始行（从0开始）
            end_row (Optional[int]): 结束行（不包含），None表示读取到最后
            
        Returns:
            List[Dict[str, Any]]: 数据行列表
        """
        try:
            # 读取数据
            if end_row is not None:
                nrows = end_row - start_row
                df = pd.read_excel(
                    self.file_path, 
                    sheet_name=sheet_name, 
                    skiprows=start_row,
                    nrows=nrows
                )
            else:
                df = pd.read_excel(
                    self.file_path, 
                    sheet_name=sheet_name, 
                    skiprows=start_row
                )
            
            # 处理NaN值
            df = df.fillna('')
            
            # 转换为字典列表
            data_list = []
            for index, row in df.iterrows():
                row_dict = row.to_dict()
                # 添加行号信息
                row_dict['_row_number'] = start_row + index + 1
                data_list.append(row_dict)
            
            logger.info(f"成功读取工作表 {sheet_name}，共 {len(data_list)} 行数据")
            return data_list
            
        except Exception as e:
            logger.error(f"读取工作表 {sheet_name} 数据失败: {str(e)}")
            return []
    
    def read_all_sheets(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        读取所有工作表的数据
        
        Returns:
            Dict[str, List[Dict[str, Any]]]: 所有工作表的数据
        """
        if not self.workbook:
            if not self.load_workbook():
                return {}
        
        all_data = {}
        for sheet_name in self.sheet_names:
            all_data[sheet_name] = self.read_sheet_data(sheet_name)
        
        return all_data
    
    def get_column_names(self, sheet_name: str) -> List[str]:
        """
        获取指定工作表的列名
        
        Args:
            sheet_name (str): 工作表名称
            
        Returns:
            List[str]: 列名列表
        """
        try:
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, nrows=0)
            return list(df.columns)
        except Exception as e:
            logger.error(f"获取工作表 {sheet_name} 列名失败: {str(e)}")
            return []
    
    def validate_file_structure(self, expected_sheets: List[str]) -> Dict[str, Any]:
        """
        验证Excel文件结构
        
        Args:
            expected_sheets (List[str]): 期望的工作表名称列表
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        if not self.load_workbook():
            return {
                'valid': False,
                'error': '无法加载Excel文件',
                'missing_sheets': expected_sheets,
                'extra_sheets': []
            }
        
        missing_sheets = [sheet for sheet in expected_sheets if sheet not in self.sheet_names]
        extra_sheets = [sheet for sheet in self.sheet_names if sheet not in expected_sheets]
        
        return {
            'valid': len(missing_sheets) == 0,
            'missing_sheets': missing_sheets,
            'extra_sheets': extra_sheets,
            'actual_sheets': self.sheet_names
        }
    
    def close(self):
        """关闭工作簿"""
        if self.workbook:
            self.workbook.close()
            self.workbook = None
            logger.info("Excel工作簿已关闭")

class ExcelDataConverter:
    """Excel数据转换器"""
    
    @staticmethod
    def row_to_json(row_data: Dict[str, Any]) -> str:
        """
        将行数据转换为JSON字符串
        
        Args:
            row_data (Dict[str, Any]): 行数据字典
            
        Returns:
            str: JSON字符串
        """
        try:
            # 处理特殊数据类型
            processed_data = {}
            for key, value in row_data.items():
                if pd.isna(value):
                    processed_data[key] = None
                elif isinstance(value, (pd.Timestamp, pd.Timedelta)):
                    processed_data[key] = str(value)
                else:
                    processed_data[key] = value
            
            return json.dumps(processed_data, ensure_ascii=False, default=str)
        except Exception as e:
            logger.error(f"转换行数据为JSON失败: {str(e)}")
            return "{}"
    
    @staticmethod
    def json_to_row(json_str: str) -> Dict[str, Any]:
        """
        将JSON字符串转换为行数据
        
        Args:
            json_str (str): JSON字符串
            
        Returns:
            Dict[str, Any]: 行数据字典
        """
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.error(f"解析JSON数据失败: {str(e)}")
            return {}
