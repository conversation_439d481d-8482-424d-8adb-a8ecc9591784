"""
备份管理器
"""
import os
import shutil
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class BackupManager:
    """备份管理器"""
    
    def __init__(self, backup_dir: str = "backups"):
        """
        初始化备份管理器
        
        Args:
            backup_dir (str): 备份目录路径
        """
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
        
    def create_backup(self, source_file: str, feature_name: str) -> str:
        """
        创建备份文件
        
        Args:
            source_file (str): 源文件路径
            feature_name (str): 功能名称
            
        Returns:
            str: 备份文件路径
        """
        try:
            source_path = Path(source_file)
            
            if not source_path.exists():
                raise FileNotFoundError(f"源文件不存在: {source_file}")
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{source_path.stem}_backup_{timestamp}_{feature_name}{source_path.suffix}"
            backup_path = self.backup_dir / backup_filename
            
            # 复制文件
            shutil.copy2(source_file, backup_path)
            
            logger.info(f"成功创建备份: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"创建备份失败: {str(e)}")
            raise
    
    def list_backups(self, pattern: str = "*backup*") -> List[Dict[str, Any]]:
        """
        列出所有备份文件
        
        Args:
            pattern (str): 文件名模式
            
        Returns:
            List[Dict[str, Any]]: 备份文件信息列表
        """
        try:
            backup_files = []
            
            for backup_file in self.backup_dir.glob(pattern):
                if backup_file.is_file():
                    stat = backup_file.stat()
                    
                    # 解析文件名获取信息
                    name_parts = backup_file.stem.split('_')
                    feature_name = ""
                    timestamp_str = ""
                    
                    if len(name_parts) >= 4:
                        # 查找backup关键字的位置
                        try:
                            backup_index = name_parts.index('backup')
                            if backup_index + 2 < len(name_parts):
                                timestamp_str = f"{name_parts[backup_index + 1]}_{name_parts[backup_index + 2]}"
                                feature_name = '_'.join(name_parts[backup_index + 3:])
                        except ValueError:
                            pass
                    
                    backup_info = {
                        'filename': backup_file.name,
                        'filepath': str(backup_file),
                        'size': stat.st_size,
                        'created_time': datetime.fromtimestamp(stat.st_ctime),
                        'modified_time': datetime.fromtimestamp(stat.st_mtime),
                        'feature_name': feature_name,
                        'timestamp_str': timestamp_str
                    }
                    
                    backup_files.append(backup_info)
            
            # 按创建时间倒序排列
            backup_files.sort(key=lambda x: x['created_time'], reverse=True)
            
            logger.info(f"找到 {len(backup_files)} 个备份文件")
            return backup_files
            
        except Exception as e:
            logger.error(f"列出备份文件失败: {str(e)}")
            return []
    
    def restore_backup(self, backup_path: str, target_path: str) -> bool:
        """
        恢复备份文件
        
        Args:
            backup_path (str): 备份文件路径
            target_path (str): 目标文件路径
            
        Returns:
            bool: 是否恢复成功
        """
        try:
            backup_file = Path(backup_path)
            target_file = Path(target_path)
            
            if not backup_file.exists():
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")
            
            # 如果目标文件存在，先创建一个临时备份
            temp_backup = None
            if target_file.exists():
                temp_backup = self.create_backup(str(target_file), "restore_temp")
            
            try:
                # 复制备份文件到目标位置
                shutil.copy2(backup_path, target_path)
                logger.info(f"成功恢复备份: {backup_path} -> {target_path}")
                
                # 删除临时备份
                if temp_backup:
                    os.remove(temp_backup)
                
                return True
                
            except Exception as e:
                # 如果恢复失败，尝试恢复临时备份
                if temp_backup and Path(temp_backup).exists():
                    shutil.copy2(temp_backup, target_path)
                    os.remove(temp_backup)
                    logger.warning("恢复失败，已回滚到原始文件")
                raise e
                
        except Exception as e:
            logger.error(f"恢复备份失败: {str(e)}")
            return False
    
    def delete_backup(self, backup_path: str) -> bool:
        """
        删除备份文件
        
        Args:
            backup_path (str): 备份文件路径
            
        Returns:
            bool: 是否删除成功
        """
        try:
            backup_file = Path(backup_path)
            
            if not backup_file.exists():
                logger.warning(f"备份文件不存在: {backup_path}")
                return True
            
            # 确保文件在备份目录中
            if not str(backup_file.resolve()).startswith(str(self.backup_dir.resolve())):
                raise ValueError("只能删除备份目录中的文件")
            
            backup_file.unlink()
            logger.info(f"成功删除备份文件: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"删除备份文件失败: {str(e)}")
            return False
    
    def cleanup_old_backups(self, days: int = 30, max_count: int = 50) -> int:
        """
        清理旧的备份文件
        
        Args:
            days (int): 保留天数
            max_count (int): 最大保留数量
            
        Returns:
            int: 删除的文件数量
        """
        try:
            backups = self.list_backups()
            deleted_count = 0
            
            # 按时间排序，最新的在前面
            backups.sort(key=lambda x: x['created_time'], reverse=True)
            
            cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
            
            for i, backup in enumerate(backups):
                should_delete = False
                
                # 超过最大数量限制
                if i >= max_count:
                    should_delete = True
                    logger.debug(f"备份文件超过数量限制: {backup['filename']}")
                
                # 超过时间限制
                elif backup['created_time'].timestamp() < cutoff_time:
                    should_delete = True
                    logger.debug(f"备份文件超过时间限制: {backup['filename']}")
                
                if should_delete:
                    if self.delete_backup(backup['filepath']):
                        deleted_count += 1
            
            logger.info(f"清理完成，删除了 {deleted_count} 个旧备份文件")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理旧备份文件失败: {str(e)}")
            return 0
    
    def get_backup_info(self, backup_path: str) -> Optional[Dict[str, Any]]:
        """
        获取备份文件详细信息
        
        Args:
            backup_path (str): 备份文件路径
            
        Returns:
            Optional[Dict[str, Any]]: 备份文件信息
        """
        try:
            backup_file = Path(backup_path)
            
            if not backup_file.exists():
                return None
            
            stat = backup_file.stat()
            
            return {
                'filename': backup_file.name,
                'filepath': str(backup_file),
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'created_time': datetime.fromtimestamp(stat.st_ctime),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'is_readable': os.access(backup_path, os.R_OK),
                'is_writable': os.access(backup_path, os.W_OK)
            }
            
        except Exception as e:
            logger.error(f"获取备份文件信息失败: {str(e)}")
            return None
