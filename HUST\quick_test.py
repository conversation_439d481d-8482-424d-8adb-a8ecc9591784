"""
快速测试脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("开始测试...")

try:
    # 测试导入
    from utils.logger import init_main_logger
    print("✓ 成功导入 logger")
    
    # 测试日志初始化
    logger = init_main_logger(
        log_file="test.log",
        level="INFO",
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    print("✓ 日志初始化成功")
    
    logger.info("这是一条测试日志")
    print("✓ 日志记录成功")
    
    print("\n🎉 修复成功！")
    
except Exception as e:
    print(f"✗ 错误: {str(e)}")
    import traceback
    traceback.print_exc()
