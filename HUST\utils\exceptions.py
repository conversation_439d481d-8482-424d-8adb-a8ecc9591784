"""
自定义异常模块
"""

class HUSTException(Exception):
    """HUST系统基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        """
        初始化异常
        
        Args:
            message (str): 错误消息
            error_code (str): 错误代码
            details (dict): 错误详情
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "HUST_ERROR"
        self.details = details or {}
    
    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message

class FileOperationError(HUSTException):
    """文件操作异常"""
    
    def __init__(self, message: str, file_path: str = None, operation: str = None):
        super().__init__(message, "FILE_ERROR")
        self.file_path = file_path
        self.operation = operation
        self.details.update({
            "file_path": file_path,
            "operation": operation
        })

class ExcelReadError(FileOperationError):
    """Excel读取异常"""
    
    def __init__(self, message: str, file_path: str = None, sheet_name: str = None):
        super().__init__(message, file_path, "read")
        self.error_code = "EXCEL_READ_ERROR"
        self.sheet_name = sheet_name
        self.details["sheet_name"] = sheet_name

class ExcelWriteError(FileOperationError):
    """Excel写入异常"""
    
    def __init__(self, message: str, file_path: str = None, sheet_name: str = None):
        super().__init__(message, file_path, "write")
        self.error_code = "EXCEL_WRITE_ERROR"
        self.sheet_name = sheet_name
        self.details["sheet_name"] = sheet_name

class DatabaseError(HUSTException):
    """数据库操作异常"""
    
    def __init__(self, message: str, sql: str = None, params: tuple = None):
        super().__init__(message, "DATABASE_ERROR")
        self.sql = sql
        self.params = params
        self.details.update({
            "sql": sql,
            "params": params
        })

class ValidationError(HUSTException):
    """数据验证异常"""
    
    def __init__(self, message: str, field_name: str = None, field_value = None, 
                 rule_name: str = None):
        super().__init__(message, "VALIDATION_ERROR")
        self.field_name = field_name
        self.field_value = field_value
        self.rule_name = rule_name
        self.details.update({
            "field_name": field_name,
            "field_value": field_value,
            "rule_name": rule_name
        })

class ConversionError(HUSTException):
    """数据转换异常"""
    
    def __init__(self, message: str, source_field: str = None, target_field: str = None,
                 conversion_type: str = None):
        super().__init__(message, "CONVERSION_ERROR")
        self.source_field = source_field
        self.target_field = target_field
        self.conversion_type = conversion_type
        self.details.update({
            "source_field": source_field,
            "target_field": target_field,
            "conversion_type": conversion_type
        })

class ConfigurationError(HUSTException):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: str = None, config_value = None):
        super().__init__(message, "CONFIG_ERROR")
        self.config_key = config_key
        self.config_value = config_value
        self.details.update({
            "config_key": config_key,
            "config_value": config_value
        })

class BackupError(HUSTException):
    """备份操作异常"""
    
    def __init__(self, message: str, backup_path: str = None, operation: str = None):
        super().__init__(message, "BACKUP_ERROR")
        self.backup_path = backup_path
        self.operation = operation
        self.details.update({
            "backup_path": backup_path,
            "operation": operation
        })

class ProcessingError(HUSTException):
    """数据处理异常"""
    
    def __init__(self, message: str, processor_name: str = None, row_number: int = None):
        super().__init__(message, "PROCESSING_ERROR")
        self.processor_name = processor_name
        self.row_number = row_number
        self.details.update({
            "processor_name": processor_name,
            "row_number": row_number
        })

class BusinessRuleError(HUSTException):
    """业务规则异常"""
    
    def __init__(self, message: str, rule_name: str = None, rule_params: dict = None):
        super().__init__(message, "BUSINESS_RULE_ERROR")
        self.rule_name = rule_name
        self.rule_params = rule_params
        self.details.update({
            "rule_name": rule_name,
            "rule_params": rule_params
        })

def handle_exception(func):
    """
    异常处理装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except HUSTException:
            # 重新抛出HUST异常
            raise
        except FileNotFoundError as e:
            raise FileOperationError(f"文件未找到: {str(e)}", operation="access")
        except PermissionError as e:
            raise FileOperationError(f"文件权限错误: {str(e)}", operation="access")
        except Exception as e:
            # 将其他异常包装为HUST异常
            raise HUSTException(f"未知错误: {str(e)}", "UNKNOWN_ERROR")
    
    return wrapper

class ErrorCollector:
    """错误收集器"""
    
    def __init__(self, max_errors: int = 100):
        """
        初始化错误收集器
        
        Args:
            max_errors (int): 最大错误数量
        """
        self.max_errors = max_errors
        self.errors = []
        self.warnings = []
    
    def add_error(self, error: Exception, context: dict = None):
        """
        添加错误
        
        Args:
            error (Exception): 错误对象
            context (dict): 错误上下文
        """
        if len(self.errors) >= self.max_errors:
            return
        
        error_info = {
            "error": error,
            "message": str(error),
            "type": type(error).__name__,
            "context": context or {}
        }
        
        self.errors.append(error_info)
    
    def add_warning(self, message: str, context: dict = None):
        """
        添加警告
        
        Args:
            message (str): 警告消息
            context (dict): 警告上下文
        """
        warning_info = {
            "message": message,
            "context": context or {}
        }
        
        self.warnings.append(warning_info)
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warnings) > 0
    
    def get_error_summary(self) -> dict:
        """获取错误摘要"""
        return {
            "error_count": len(self.errors),
            "warning_count": len(self.warnings),
            "max_errors_reached": len(self.errors) >= self.max_errors,
            "errors": self.errors,
            "warnings": self.warnings
        }
    
    def clear(self):
        """清空错误和警告"""
        self.errors.clear()
        self.warnings.clear()
