"""
机组验证器启动脚本
双击此文件即可启动机组验证器
"""
import os
import sys

# 确保当前目录在Python路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

try:
    # 导入并运行机组验证器应用
    from machine_validator_app import main
    main()
except ImportError as e:
    import tkinter as tk
    from tkinter import messagebox
    
    root = tk.Tk()
    root.withdraw()
    messagebox.showerror(
        "导入错误", 
        f"无法导入必要的模块：\n{str(e)}\n\n请确保所有文件都在正确的位置。"
    )
except Exception as e:
    import tkinter as tk
    from tkinter import messagebox
    
    root = tk.Tk()
    root.withdraw()
    messagebox.showerror(
        "运行错误", 
        f"程序运行时出现错误：\n{str(e)}"
    )
