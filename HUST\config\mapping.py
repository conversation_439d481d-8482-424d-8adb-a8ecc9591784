"""
字段映射配置
定义源文件和目标文件之间的字段映射关系
"""

# 直接映射配置
# 格式: "源字段名": "目标字段名"
DIRECT_MAPPING = {
    # 示例映射，需要根据实际文件结构调整
    "机组名称": "机组名称",
    "装机容量": "装机容量",
    "机组类型": "机组类型",
    "所属电厂": "所属电厂",
    "投运年份": "投运年份",
    "燃料类型": "燃料类型"
}

# 公式映射配置
# 格式: "目标字段名": {"type": "formula", "formula": "计算公式", "dependencies": ["依赖字段"]}
FORMULA_MAPPING = {
    "总装机容量": {
        "type": "formula",
        "formula": "装机容量 * 机组数量",
        "dependencies": ["装机容量", "机组数量"],
        "description": "计算总装机容量"
    },
    "容量等级": {
        "type": "formula", 
        "formula": "get_capacity_level(装机容量)",
        "dependencies": ["装机容量"],
        "description": "根据装机容量确定容量等级"
    }
}

# 查找表映射配置
# 格式: "目标字段名": {"type": "lookup", "lookup_table": "查找表名", "key_field": "关键字段"}
LOOKUP_MAPPING = {
    "标准机组类型": {
        "type": "lookup",
        "lookup_table": "machine_type_mapping",
        "key_field": "机组类型",
        "value_field": "标准类型",
        "description": "将机组类型标准化"
    },
    "区域代码": {
        "type": "lookup",
        "lookup_table": "region_mapping",
        "key_field": "所属电厂",
        "value_field": "区域代码",
        "description": "根据电厂名称获取区域代码"
    }
}

# 自定义函数映射配置
# 格式: "目标字段名": {"type": "custom", "function": "函数名", "params": {参数}}
CUSTOM_MAPPING = {
    "机组编号": {
        "type": "custom",
        "function": "generate_machine_code",
        "params": {
            "prefix": "HUST",
            "fields": ["所属电厂", "机组类型", "装机容量"]
        },
        "description": "生成标准机组编号"
    },
    "环保等级": {
        "type": "custom",
        "function": "calculate_env_level",
        "params": {
            "base_field": "投运年份",
            "type_field": "机组类型"
        },
        "description": "根据投运年份和机组类型计算环保等级"
    }
}

# 字符串处理映射配置
STRING_MAPPING = {
    "标准电厂名称": {
        "type": "string_process",
        "operation": "clean_plant_name",
        "source_field": "所属电厂",
        "params": {
            "remove_suffixes": ["电厂", "发电厂", "发电有限公司"],
            "standardize": True
        },
        "description": "清理和标准化电厂名称"
    }
}

# 条件映射配置
CONDITIONAL_MAPPING = {
    "机组状态": {
        "type": "conditional",
        "conditions": [
            {
                "condition": "投运年份 >= 2020",
                "value": "新机组"
            },
            {
                "condition": "投运年份 >= 2010",
                "value": "较新机组"
            },
            {
                "condition": "投运年份 >= 2000",
                "value": "一般机组"
            }
        ],
        "default_value": "老旧机组",
        "description": "根据投运年份确定机组状态"
    }
}

# 查找表数据
LOOKUP_TABLES = {
    "machine_type_mapping": {
        "燃煤机组": "火电",
        "燃气机组": "火电", 
        "水电机组": "水电",
        "风电机组": "风电",
        "光伏机组": "光伏",
        "核电机组": "核电"
    },
    "region_mapping": {
        "华中电厂": "HC",
        "华东电厂": "HD",
        "华北电厂": "HB",
        "华南电厂": "HN",
        "西北电厂": "XB",
        "东北电厂": "DB",
        "西南电厂": "XN"
    }
}

# 数据验证规则
VALIDATION_RULES = {
    "装机容量": {
        "type": "range",
        "min_value": 0,
        "max_value": 10000,
        "error_message": "装机容量必须在0-10000MW之间"
    },
    "投运年份": {
        "type": "range", 
        "min_value": 1950,
        "max_value": 2030,
        "error_message": "投运年份必须在1950-2030之间"
    },
    "机组名称": {
        "type": "required",
        "error_message": "机组名称不能为空"
    }
}

# 字段优先级配置（用于处理冲突）
FIELD_PRIORITY = {
    "机组名称": 1,
    "装机容量": 2,
    "机组类型": 3,
    "所属电厂": 4,
    "投运年份": 5
}

def get_all_mappings():
    """
    获取所有映射配置
    
    Returns:
        dict: 包含所有映射类型的字典
    """
    return {
        "direct": DIRECT_MAPPING,
        "formula": FORMULA_MAPPING,
        "lookup": LOOKUP_MAPPING,
        "custom": CUSTOM_MAPPING,
        "string": STRING_MAPPING,
        "conditional": CONDITIONAL_MAPPING
    }

def get_target_fields():
    """
    获取所有目标字段列表
    
    Returns:
        list: 目标字段名称列表
    """
    target_fields = set()
    
    # 直接映射的目标字段
    target_fields.update(DIRECT_MAPPING.values())
    
    # 其他映射类型的目标字段
    for mapping_dict in [FORMULA_MAPPING, LOOKUP_MAPPING, CUSTOM_MAPPING, 
                        STRING_MAPPING, CONDITIONAL_MAPPING]:
        target_fields.update(mapping_dict.keys())
    
    return sorted(list(target_fields))

def get_source_fields():
    """
    获取所有源字段列表
    
    Returns:
        list: 源字段名称列表
    """
    source_fields = set()
    
    # 直接映射的源字段
    source_fields.update(DIRECT_MAPPING.keys())
    
    # 公式映射的依赖字段
    for config in FORMULA_MAPPING.values():
        if "dependencies" in config:
            source_fields.update(config["dependencies"])
    
    # 查找映射的关键字段
    for config in LOOKUP_MAPPING.values():
        if "key_field" in config:
            source_fields.add(config["key_field"])
    
    # 自定义映射的参数字段
    for config in CUSTOM_MAPPING.values():
        if "params" in config and "fields" in config["params"]:
            source_fields.update(config["params"]["fields"])
    
    # 字符串处理的源字段
    for config in STRING_MAPPING.values():
        if "source_field" in config:
            source_fields.add(config["source_field"])
    
    return sorted(list(source_fields))

def validate_mapping_config():
    """
    验证映射配置的完整性和正确性
    
    Returns:
        dict: 验证结果
    """
    errors = []
    warnings = []
    
    # 检查循环依赖
    # TODO: 实现循环依赖检查逻辑
    
    # 检查查找表完整性
    for field, config in LOOKUP_MAPPING.items():
        table_name = config.get("lookup_table")
        if table_name not in LOOKUP_TABLES:
            errors.append(f"查找表 {table_name} 未定义 (字段: {field})")
    
    # 检查必需字段
    required_fields = ["机组名称", "装机容量"]
    target_fields = get_target_fields()
    for field in required_fields:
        if field not in target_fields:
            warnings.append(f"建议包含必需字段: {field}")
    
    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "warnings": warnings
    }
