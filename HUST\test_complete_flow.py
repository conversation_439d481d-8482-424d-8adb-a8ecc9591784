"""
完整流程测试脚本
测试从电站转换到机组转换的完整流程
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import init_main_logger, get_main_logger
from processors.station_converter import StationConverter
from processors.unit_converter import UnitConverter
from config.settings import FILES

def test_station_conversion():
    """测试电站转换"""
    print("=" * 50)
    print("测试电站转换")
    print("=" * 50)
    
    try:
        # 文件路径
        source_file = project_root / FILES['source_file']
        target_file = project_root / FILES['target_file']
        
        # 创建转换器
        converter = StationConverter(str(source_file), str(target_file))
        
        # 获取预览
        print("获取电站转换预览...")
        preview = converter.get_conversion_preview()
        
        if preview:
            print(f"源文件总记录数: {preview.get('total_source_records', 0)}")
            print(f"符合条件的记录数: {preview.get('filtered_records', 0)}")
            print(f"唯一项目数: {preview.get('unique_projects', 0)}")
            
            print("\n预览生成的电站名称（前5个）:")
            for i, station in enumerate(preview.get('preview_stations', [])[:5], 1):
                print(f"  {i}. {station['original_project']} -> {station['generated_station']}")
        
        # 执行转换
        print("\n执行电站转换...")
        success = converter.convert_projects_to_stations()
        
        if success:
            print("✅ 电站转换成功")
            return True
        else:
            print("❌ 电站转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 电站转换测试失败: {str(e)}")
        return False

def test_unit_conversion():
    """测试机组转换"""
    print("\n" + "=" * 50)
    print("测试机组转换")
    print("=" * 50)
    
    try:
        # 文件路径
        source_file = project_root / FILES['source_file']
        target_file = project_root / FILES['target_file']
        
        # 创建转换器
        converter = UnitConverter(str(source_file), str(target_file))
        
        # 获取预览
        print("获取机组转换预览...")
        preview = converter.get_conversion_preview()
        
        if 'error' in preview:
            print(f"❌ 预览失败: {preview['error']}")
            return False
        
        print(f"电站数量: {preview.get('station_count', 0)}")
        print(f"源文件记录数: {preview.get('source_records', 0)}")
        
        print("\n预览生成的机组（前5个）:")
        for i, unit in enumerate(preview.get('preview_units', [])[:5], 1):
            print(f"  {i}. {unit['station_name']} -> {unit['unit_name']}")
            print(f"     容量: {unit['unit_capacity']} MW, 类型: {unit['unit_type']}")
        
        # 执行转换
        print("\n执行机组转换...")
        success = converter.convert_stations_to_units()
        
        if success:
            print("✅ 机组转换成功")
            return True
        else:
            print("❌ 机组转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 机组转换测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    # 初始化日志
    logger = init_main_logger(
        log_file="complete_flow_test.log",
        level="INFO"
    )
    
    print("HUST数据转换系统 - 完整流程测试")
    print("=" * 60)
    
    try:
        # 检查文件是否存在
        source_file = project_root / FILES['source_file']
        target_file = project_root / FILES['target_file']
        
        if not source_file.exists():
            print(f"❌ 源文件不存在: {source_file}")
            return False
        
        if not target_file.exists():
            print(f"❌ 目标文件不存在: {target_file}")
            return False
        
        print(f"✅ 源文件: {source_file}")
        print(f"✅ 目标文件: {target_file}")
        
        # 询问用户选择
        print("\n请选择测试模式:")
        print("1. 只测试电站转换")
        print("2. 只测试机组转换")
        print("3. 测试完整流程（电站 -> 机组）")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            return test_station_conversion()
        elif choice == '2':
            return test_unit_conversion()
        elif choice == '3':
            # 完整流程测试
            print("\n开始完整流程测试...")
            
            # 第一步：电站转换
            station_success = test_station_conversion()
            if not station_success:
                print("❌ 电站转换失败，停止测试")
                return False
            
            # 第二步：机组转换
            unit_success = test_unit_conversion()
            if not unit_success:
                print("❌ 机组转换失败")
                return False
            
            print("\n🎉 完整流程测试成功！")
            return True
        elif choice == '4':
            print("退出测试")
            return True
        else:
            print("❌ 无效选择")
            return False
            
    except Exception as e:
        logger.error(f"测试程序执行出错: {str(e)}")
        print(f"\n❌ 测试程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
