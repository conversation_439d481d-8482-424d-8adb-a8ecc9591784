"""
数据模型定义
"""
import json
from dataclasses import dataclass, asdict
from datetime import datetime
from typing import Optional, Dict, Any, List

@dataclass
class SourceData:
    """源数据模型"""
    id: Optional[int] = None
    raw_data: str = ""
    sheet_name: str = ""
    row_number: int = 0
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SourceData':
        """从字典创建实例"""
        return cls(**data)
    
    def get_raw_data_dict(self) -> Dict[str, Any]:
        """获取原始数据的字典形式"""
        try:
            return json.loads(self.raw_data)
        except json.JSONDecodeError:
            return {}

@dataclass
class ConversionRule:
    """转换规则模型"""
    id: Optional[int] = None
    rule_name: str = ""
    source_field: str = ""
    target_field: str = ""
    conversion_type: str = ""  # direct, lookup, formula, custom
    conversion_params: str = ""  # JSON格式参数
    is_active: bool = True
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversionRule':
        """从字典创建实例"""
        return cls(**data)
    
    def get_params_dict(self) -> Dict[str, Any]:
        """获取转换参数的字典形式"""
        try:
            return json.loads(self.conversion_params) if self.conversion_params else {}
        except json.JSONDecodeError:
            return {}
    
    def set_params_dict(self, params: Dict[str, Any]):
        """设置转换参数"""
        self.conversion_params = json.dumps(params, ensure_ascii=False)

@dataclass
class ValidationRule:
    """验证规则模型"""
    id: Optional[int] = None
    rule_name: str = ""
    field_name: str = ""
    rule_type: str = ""  # required, format, range, custom
    rule_params: str = ""  # JSON格式参数
    error_message: str = ""
    is_active: bool = True
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ValidationRule':
        """从字典创建实例"""
        return cls(**data)
    
    def get_params_dict(self) -> Dict[str, Any]:
        """获取验证参数的字典形式"""
        try:
            return json.loads(self.rule_params) if self.rule_params else {}
        except json.JSONDecodeError:
            return {}
    
    def set_params_dict(self, params: Dict[str, Any]):
        """设置验证参数"""
        self.rule_params = json.dumps(params, ensure_ascii=False)

@dataclass
class ConversionLog:
    """转换日志模型"""
    id: Optional[int] = None
    operation_type: str = ""  # load, convert, validate, backup
    source_file: str = ""
    target_file: str = ""
    backup_file: str = ""
    status: str = ""  # success, error, warning
    error_message: str = ""
    records_processed: int = 0
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversionLog':
        """从字典创建实例"""
        return cls(**data)

# 转换类型枚举
class ConversionType:
    """转换类型常量"""
    DIRECT = "direct"           # 直接映射
    LOOKUP = "lookup"           # 查找表映射
    FORMULA = "formula"         # 公式计算
    CUSTOM = "custom"           # 自定义函数
    CONCAT = "concat"           # 字符串拼接
    SPLIT = "split"             # 字符串分割
    FORMAT = "format"           # 格式化

# 验证类型枚举
class ValidationType:
    """验证类型常量"""
    REQUIRED = "required"       # 必填验证
    FORMAT = "format"           # 格式验证
    RANGE = "range"             # 范围验证
    CUSTOM = "custom"           # 自定义验证
    UNIQUE = "unique"           # 唯一性验证
    REFERENCE = "reference"     # 引用验证

# 操作状态枚举
class OperationStatus:
    """操作状态常量"""
    SUCCESS = "success"         # 成功
    ERROR = "error"             # 错误
    WARNING = "warning"         # 警告
    PENDING = "pending"         # 待处理
    PROCESSING = "processing"   # 处理中
