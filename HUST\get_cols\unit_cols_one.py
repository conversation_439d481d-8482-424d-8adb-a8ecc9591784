import pandas as pd

def unit_get_cols_one_row(data, cols_name, type_val):
    """添加新列并设置默认值"""
    # 赋值
    if type_val == 'SD_KT':
        data['电站ID'] = 56
        for col_name in cols_name:
            if col_name == '动态投资':
                data[col_name] = 10000
            elif col_name == '燃料单价':
                data[col_name] = 0
            elif col_name == '燃料单耗':
                data[col_name] = 0
            elif col_name == '检修天数':
                data[col_name] = 30
            elif col_name == '特性ID':
                data[col_name] = 1
            elif col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax' or col_name == '台数':
                data[col_name] = 1
            elif col_name == '上网电价' or col_name == '汛期电价':
                data[col_name] = 0.3296
            elif col_name == '技术出力':
                data[col_name] = 0.5
            elif col_name == '爬坡率':
                data[col_name] = 0.75
            elif col_name == '运维费率':
                data[col_name] = 0.01
            elif col_name == '运行费':
                data[col_name] = 0.001
            elif col_name == '类型' or col_name == '储能库容' or col_name == '退役年月' or col_name == '退役进度' or col_name == '变电投资' or col_name == '功频系数' or col_name == '惯性常数' or col_name == '投产进度':
                data[col_name] = 0
            elif col_name == '强迫停运':
                data[col_name] = 0.05


    # 处理投产年月列
    # 将所有能转换为日期的元素转换为datetime类型
    data['投产年月'] = pd.to_datetime(data['投产年月'], errors='coerce')

    # 提取年月信息并转换为整数
    year_month_series = data['投产年月'].dt.strftime('%Y%m')

    # 将年月信息更新到投产年月列
    data['投产年月'] = year_month_series

    # 设置投产进度为101的条件
    mask = (data['投产年月'].notna()) & (data['投产年月'] >= '202506')
    data.loc[mask, '投产进度'] = 101

    cols_name = ['名称', '有效性', '电站ID', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                 '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                 '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                 '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']
    return data[cols_name]
