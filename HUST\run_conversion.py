"""
直接执行电站转换（无用户交互）
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import init_main_logger, get_main_logger
from processors.station_converter import StationConverter
from config.settings import FILES

def main():
    """主函数"""
    # 初始化日志
    logger = init_main_logger(
        log_file="station_conversion.log",
        level="INFO"
    )
    
    logger.info("=" * 60)
    logger.info("电站数据转换程序启动（自动执行模式）")
    logger.info("=" * 60)
    
    try:
        # 文件路径
        source_file = project_root / FILES['source_file']
        target_file = project_root / FILES['target_file']
        
        logger.info(f"源文件: {source_file}")
        logger.info(f"目标文件: {target_file}")
        
        # 检查文件是否存在
        if not source_file.exists():
            logger.error(f"源文件不存在: {source_file}")
            print(f"❌ 源文件不存在: {source_file}")
            return False
        
        if not target_file.exists():
            logger.error(f"目标文件不存在: {target_file}")
            print(f"❌ 目标文件不存在: {target_file}")
            return False
        
        # 创建转换器
        converter = StationConverter(str(source_file), str(target_file))
        
        # 获取转换预览
        logger.info("获取转换预览...")
        print("正在分析源数据...")
        
        preview = converter.get_conversion_preview()
        
        if preview:
            print("=" * 60)
            print("转换预览:")
            print(f"源文件总记录数: {preview.get('total_source_records', 0)}")
            print(f"符合条件的记录数: {preview.get('filtered_records', 0)}")
            print(f"唯一项目数: {preview.get('unique_projects', 0)}")
            print(f"目标机组类型: {', '.join(preview.get('target_machine_types', []))}")
            
            print("\n预览生成的电站名称（前10个）:")
            for i, station in enumerate(preview.get('preview_stations', []), 1):
                print(f"  {i:2d}. {station['original_project']} -> {station['generated_station']}")
            
            if preview.get('has_more', False):
                total = preview.get('unique_projects', 0)
                shown = len(preview.get('preview_stations', []))
                print(f"  ... 还有 {total - shown} 个项目")
            
            print("=" * 60)
        
        # 直接执行转换
        print("\n开始执行转换...")
        logger.info("开始执行电站数据转换...")
        
        success = converter.convert_projects_to_stations()
        
        if success:
            logger.info("🎉 电站数据转换成功完成！")
            print("\n✅ 转换成功完成！")
            print(f"✅ 已生成 {preview.get('unique_projects', 0)} 个电站记录")
            print(f"✅ 数据已写入目标文件的'电站表'工作表")
            print(f"✅ 备份文件已自动创建")
            return True
        else:
            logger.error("❌ 电站数据转换失败")
            print("\n❌ 转换失败，请检查日志文件。")
            return False
            
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    main()
