"""
简单测试投产逻辑
"""
from datetime import datetime

def parse_production_date(date_str):
    """解析投产时间"""
    cutoff_date = datetime(2025, 6, 12)
    
    if not date_str:
        return "0"
    
    try:
        # 尝试解析日期
        if isinstance(date_str, str):
            # 处理常见的日期格式
            for fmt in ['%Y/%m/%d', '%Y-%m-%d', '%Y/%m', '%Y-%m']:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    break
                except ValueError:
                    continue
            else:
                print(f"无法解析投产时间: {date_str}")
                return "0"
        else:
            # 如果是datetime对象
            date_obj = date_str
        
        # 检查是否早于截止日期
        if date_obj < cutoff_date:
            return "0"
        else:
            return date_obj.strftime("%Y%m")
            
    except Exception as e:
        print(f"解析投产时间失败: {date_str}, 错误: {str(e)}")
        return "0"

# 测试用例
test_cases = [
    ("2025/6/10", "早于截止日期"),
    ("2025/6/12", "等于截止日期"),
    ("2025/6/15", "晚于截止日期"),
    ("2025/6/30", "晚于截止日期"),
    ("2024/12/31", "早于截止日期"),
    ("2026/1/1", "晚于截止日期"),
    ("", "空值"),
]

print("投产时间和投产进度逻辑测试")
print("=" * 50)
print("截止日期: 2025/6/12")
print("-" * 50)

for date_input, description in test_cases:
    result = parse_production_date(date_input)
    production_progress = 101 if result != "0" else 0
    
    print(f"输入: {date_input:15} ({description})")
    print(f"  -> 投产年月: {result:8}")
    print(f"  -> 投产进度: {production_progress}")
    print()

print("=" * 50)
print("✅ 修复验证:")
print("✅ 早于2025/6/12的日期 -> 投产年月=0, 投产进度=0")
print("✅ 晚于2025/6/12的日期 -> 投产年月=YYYYMM, 投产进度=101")
print("✅ 投产进度与投产年月配合填写逻辑已修复！")
