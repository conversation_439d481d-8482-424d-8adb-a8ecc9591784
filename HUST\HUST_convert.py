import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
from update.updata_station_format import update_excel_station_with_format
from update.updata_unit_format import update_excel_unit_with_format
# from update.updata_one import update_excel_one_row_format
from extract_chinese import extract_chinese_text
from type_convert.station_type import station_type_convert
from type_convert.unit_type import unit_type_convert
from type_convert.unit_type_one import unit_type_convert_one_row
from get_cols.station_cols import station_get_cols
from get_cols.unit_cols import unit_get_cols
from get_cols.unit_cols_one import unit_get_cols_one_row


def main():
    # 创建GUI窗口
    root = tk.Tk()
    root.title("Excel数据处理工具")
    root.geometry("500x450")  # 增加窗口高度以容纳新控件
    root.configure(bg="#f0f0f0")

    # 设置字体
    label_font = ("SimHei", 10)
    entry_font = ("SimHei", 10)
    button_font = ("SimHei", 10, "bold")

    # 创建变量存储输入值
    file_path = tk.StringVar()
    type_name = tk.StringVar(value="QD_9F")
    # sheet_name = tk.StringVar(value="电站表")  # sheet名称变量
    save_existing = tk.BooleanVar(value=False)  # 是否保存到现有文件
    center_alignment = tk.BooleanVar(value=True)  # 是否居中对齐
    is_single_row = tk.BooleanVar(value=False)  # 新增：是否为一行

    # 选择文件函数
    def browse_file():
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        file_path.set(filename)

    # 处理数据函数
    def process_data():
        try:
            # 获取输入值
            path = file_path.get()
            type_val = type_name.get()
            # sheet_val = sheet_name.get()
            sheet_val = "电站表"
            save_to_existing = save_existing.get()
            center_aligned = center_alignment.get()
            single_row = is_single_row.get()  # 获取新增选项的值

            if not path or not type_val:
                messagebox.showerror("错误", "请输入文件路径和机组类型")
                return

            # 读取数据
            data = pd.read_excel(path, sheet_name='0226新数据', engine='openpyxl')

            # 新列名称
            station_cols_name = ['有效性', '节点ID', '类型', '检修场地', '备用Rmax',
                                 '储能比率', '储能效率', '储能损耗', '期望电量',
                                 '最小电量', '电站约束', '流域ID', '优化空间']
            # 电站ID和名称已有
            unit_cols_name = ['有效性', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                              '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                              '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                              '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']

            # 处理"中调水电"特殊情况
            if single_row:
                # 创建"中调水电"的电站数据
                if type_val == '中调水电':
                    station_data = pd.DataFrame({
                        '电站ID':56,
                        '名称': ['中调水电'],
                        '有效性':1,
                        '节点ID':1,
                        '类型':371,
                        '检修场地':0,
                        '备用Rmax':0.1,
                        '储能比率':0,
                        '储能效率':0,
                        '储能损耗':0,
                        '期望电量':1,
                        '最小电量':1,
                        '电站约束':0,
                        '流域ID':0,
                        '优化空间':0,
                    })
            # 筛选特定机组类型的数据
            else:
                station_data = station_type_convert(data, type_val)

                # 添加新列并设置默认值
                station_data = station_get_cols(station_data, station_cols_name, type_val)

            # 根据用户选择保存数据
            if save_to_existing:
                # 让用户选择目标文件路径
                save_path = filedialog.askopenfilename(
                    title="选择要保存到的Excel文件",
                    filetypes=[("Excel files", "*.xlsx *.xls")]
                )

                if not save_path:
                    return  # 用户取消了操作

                # 使用保留格式的方法保存到选择的文件
                if not single_row:
                    name_id_mapping = update_excel_station_with_format(
                        station_data,
                        save_path,
                        sheet_val,
                        key_column='名称',  # 使用项目名称作为键列
                        center_alignment=center_aligned
                    )
                else:
                    update_excel_one_row_format(
                        station_data,
                        save_path,
                        sheet_val,
                        key_column='名称',  # 使用项目名称作为键列
                        center_alignment=center_aligned
                    )

                messagebox.showinfo("成功", f"数据已成功添加到 {save_path} 的 {sheet_val} 工作表，保留原有格式")
            else:
                # 保存为新文件
                save_path = filedialog.asksaveasfilename(
                    title="保存文件",
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx")]
                )

                if save_path:
                    # 为新文件生成连续的电站ID
                    if not station_data.empty:
                        station_data['电站ID'] = range(1, len(station_data) + 1)

                    # 创建项目名称与电站ID的映射
                    name_id_mapping = {}
                    for index, row in station_data.iterrows():
                        # 提取原始项目名称
                        original_name = extract_chinese_text(row['名称'])
                        name_id_mapping[original_name] = row['电站ID']

                    station_data.to_excel(save_path, sheet_name=sheet_val, index=False)
                    messagebox.showinfo("成功", f"数据已成功保存到 {save_path}")
            if not single_row:
                unit_data = unit_type_convert(data, type_val, name_id_mapping)

                # 传递新增的single_row参数
                unit_data = unit_get_cols(unit_data, unit_cols_name, type_val)
            else:
                if type_val == '中调水电':
                    unit_data = unit_type_convert_one_row(data, type_val)

                    unit_data = unit_get_cols_one_row(unit_data, unit_cols_name, type_val)
            # 让用户选择保存机组数据的文件
            unit_file_path = filedialog.askopenfilename(
                title="选择要保存机组数据的Excel文件",
                filetypes=[("Excel files", "*.xlsx *.xls")]
            )

            if unit_file_path:
                update_excel_unit_with_format(
                    unit_data,
                    unit_file_path,
                    '机组表',  # 固定使用"机组表"作为sheet名称
                    key_column='名称',  # 使用项目名称作为键列
                    center_alignment=center_aligned
                )
                messagebox.showinfo("成功", f"机组数据已成功保存到 {unit_file_path} 的 机组表 工作表")
            root.destroy()  # 处理完成后关闭窗口

        except Exception as e:
            messagebox.showerror("处理错误", f"发生错误: {str(e)}")

    # 创建界面元素
    frame = tk.Frame(root, bg="#f0f0f0", padx=20, pady=20)
    frame.pack(fill=tk.BOTH, expand=True)

    # 文件路径选择
    tk.Label(frame, text="Excel文件路径:", font=label_font, bg="#f0f0f0").grid(row=0, column=0, sticky=tk.W, pady=10)
    tk.Entry(frame, textvariable=file_path, width=40, font=entry_font).grid(row=0, column=1, pady=10, padx=5)
    tk.Button(frame, text="浏览...", command=browse_file, font=button_font, bg="#4CAF50", fg="white").grid(row=0,
                                                                                                         column=2,
                                                                                                         pady=10,
                                                                                                         padx=5)

    # 机组类型输入
    tk.Label(frame, text="机组类型:", font=label_font, bg="#f0f0f0").grid(row=1, column=0, sticky=tk.W, pady=10)

    # 创建下拉菜单选择机组类型
    type_options = ["QD_9F", "QD_RD", "HD", "中调水电", "XN", "陆上风电", "海上风电", "光伏"]
    type_combobox = tk.StringVar(value="QD_9F")
    type_dropdown = tk.OptionMenu(frame, type_combobox, *type_options)
    type_dropdown.config(font=entry_font, width=35)
    type_dropdown.grid(row=1, column=1, pady=10, padx=5, sticky=tk.W)

    # 更新type_name变量以使用下拉菜单的值
    def update_type_name(*args):
        type_name.set(type_combobox.get())
    type_combobox.trace('w', update_type_name)
    type_name.set(type_combobox.get())  # 初始化

    # 是否保存到现有文件的选项
    tk.Checkbutton(frame, text="保存到现有文件（保留其他sheet）", variable=save_existing, font=label_font, bg="#f0f0f0").grid(row=3,
                                                                                                                 column=0,
                                                                                                                 columnspan=3,
                                                                                                                 sticky=tk.W,
                                                                                                                 pady=10)

    # 是否居中对齐的选项
    tk.Checkbutton(frame, text="设置单元格居中对齐", variable=center_alignment, font=label_font, bg="#f0f0f0").grid(row=4,
                                                                                                           column=0,
                                                                                                           columnspan=3,
                                                                                                           sticky=tk.W,
                                                                                                           pady=10)

    # 新增：是否为一行的选项
    tk.Checkbutton(frame, text="是否为一行", variable=is_single_row, font=label_font, bg="#f0f0f0").grid(row=5,
                                                                                                       column=0,
                                                                                                       columnspan=3,
                                                                                                       sticky=tk.W,
                                                                                                       pady=10)

    # 处理按钮
    tk.Button(frame, text="开始处理", command=process_data, font=button_font, bg="#2196F3", fg="white", height=2,
              width=15).grid(row=6, column=0, columnspan=3, pady=20)

    # 版权信息
    tk.Label(root, text="Excel数据处理工具 © 2025", font=("SimHei", 9), bg="#f0f0f0", fg="#666666").pack(side=tk.BOTTOM,
                                                                                                   pady=10)

    # 运行主循环
    root.mainloop()


def station_type_convert(data, type_name):
    # 选择“机组类型”为type_name的行
    # 电源明细表 -> 电站表
    data = data[(data['机组类型'] == type_name)]
    data = data[data.机组类型.isin([type_name])]
    data = pd.DataFrame(data, columns=['项目名称', '机组类型'])
    data.drop_duplicates(subset='项目名称', keep='last', inplace=True, ignore_index=False)

    # 修改索引
    data = data.reset_index(drop=True)

    # “机组类型”添加到“项目名称”前置
    type_name = type_name + '_'
    for i in range(len(data)):
        data['项目名称'][i] = type_name[:3] + str(i + 1) + type_name[2:6] + data['项目名称'][i]

    # 只保留“项目名称”列重命名
    data = pd.DataFrame(data['项目名称'])
    data.rename(columns={'项目名称': '名称'}, inplace=True)
    return data


def unit_type_convert(data, type_name, name_id_mapping):
    # 选择“机组类型”为type_name的行
    # 电源明细表 -> 机组表
    data = data[(data['机组类型'] == type_name)]
    data = data[data.机组类型.isin([type_name])]
    # 修改索引
    data = data.reset_index(drop=True)
    # 根据"名称"对应字典找到"电站ID"
    data['电站ID'] = 0
    for i in range(len(data)):
        data['电站ID'][i] = name_id_mapping[data['项目名称'][i]]
    # “机组类型”添加到“项目名称”前置
    type_name = type_name + '_'
    for i in range(len(data)):
        data['项目名称'][i] = type_name[:3] + str(i + 1) + type_name[2:6] + data['项目名称'][i]
    # “机组序号”添加到“项目名称”后置
    for i in range(len(data)):
        data['项目名称'][i] = data['项目名称'][i] + '_' + str(data['机组序号'][i])
    # 只保留“项目类型”列
    data.rename(columns={'项目名称': '名称'}, inplace=True)
    return data


# def station_get_cols(data, cols_name):
#     """添加新列并设置默认值"""
#     for col_name in cols_name:
#         if col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax':
#             data[col_name] = 1
#         elif col_name == '类型':
#             data[col_name] = 320
#         else:
#             data[col_name] = 0
#     return data

# def unit_get_cols(data, cols_name):
#     """添加新列并设置默认值"""
#     # 重命名
#     data.rename(columns={'机组容量': '单机容量', '投产时间': '投产年月'}, inplace=True)

#     # 赋值
#     for col_name in cols_name:
#         if col_name == '动态投资':
#             data[col_name] = 2700
#         elif col_name == '燃料单价':
#             data[col_name] = 1654
#         elif col_name == '燃料单耗':
#             data[col_name] = 220
#         elif col_name == '检修天数':
#             data[col_name] = 30
#         elif col_name == '特性ID':
#             data[col_name] = 5
#         elif col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax' or col_name == '台数':
#             data[col_name] = 1
#         elif col_name == '上网电价' or col_name == '汛期电价':
#             data[col_name] = 0.585
#         elif col_name == '技术出力':
#             data[col_name] = 0
#         elif col_name == '爬坡率' or col_name == '强迫停运':
#             data[col_name] = 0.05
#         elif col_name == '运维费率':
#             data[col_name] = 0.04
#         elif col_name == '运行费':
#             data[col_name] = 0.02
#         elif col_name == '类型' or col_name == '储能库容' or col_name == '退役年月' or col_name == '退役进度' or col_name == '变电投资' or col_name == '功频系数' or col_name == '惯性常数' or col_name == '投产进度':
#             data[col_name] = 0

#     # 处理投产年月列
#     # 将所有能转换为日期的元素转换为datetime类型
#     data['投产年月'] = pd.to_datetime(data['投产年月'], errors='coerce')

#     # 提取年月信息并转换为整数
#     year_month_series = data['投产年月'].dt.strftime('%Y%m')

#     # 将年月信息更新到投产年月列
#     data['投产年月'] = year_month_series

#     # 设置投产进度为101的条件
#     mask = (data['投产年月'].notna()) & (data['投产年月'] >= '202506')
#     data.loc[mask, '投产进度'] = 101

#     cols_name = ['名称', '有效性', '电站ID', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
#                  '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
#                  '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
#                  '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']

#     return data[cols_name]

if __name__ == '__main__':
    main()