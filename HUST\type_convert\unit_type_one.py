import pandas as pd

def unit_type_convert_one_row(data, type_name):
    # 选择“机组类型”为type_name的行
    # 电源明细表 -> 机组表
    if type_name == '中调水电':
        type_name = 'SD_KT'
    data = data[(data['机组类型'] == type_name)]
    data = data[data.机组类型.isin([type_name])]
    # 修改索引
    data = data.reset_index(drop=True)
    # “机组序号”添加到“项目名称”后置
    for i in range(len(data)):
        data['项目名称'][i] = data['项目名称'][i] + str(data['机组序号'][i])

    data.rename(columns={'项目名称': '名称', '机组容量': '单机容量', '投产时间': '投产年月'}, inplace=True)
    return data
