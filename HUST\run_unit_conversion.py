"""
机组数据转换执行脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import init_main_logger, get_main_logger
from processors.unit_converter import UnitConverter
from config.settings import FILES

def main():
    """主函数"""
    # 初始化日志
    logger = init_main_logger(
        log_file="unit_conversion.log",
        level="INFO"
    )
    
    logger.info("=" * 60)
    logger.info("机组数据转换程序启动")
    logger.info("=" * 60)
    
    try:
        # 文件路径
        source_file = project_root / FILES['source_file']
        target_file = project_root / FILES['target_file']
        
        logger.info(f"源文件: {source_file}")
        logger.info(f"目标文件: {target_file}")
        
        # 检查文件是否存在
        if not source_file.exists():
            logger.error(f"源文件不存在: {source_file}")
            print(f"❌ 源文件不存在: {source_file}")
            return False
        
        if not target_file.exists():
            logger.error(f"目标文件不存在: {target_file}")
            print(f"❌ 目标文件不存在: {target_file}")
            return False
        
        # 创建转换器
        converter = UnitConverter(str(source_file), str(target_file))
        
        # 获取转换预览
        logger.info("获取转换预览...")
        print("正在分析数据...")
        
        preview = converter.get_conversion_preview()
        
        if 'error' in preview:
            print(f"❌ 预览失败: {preview['error']}")
            return False
        
        print("=" * 60)
        print("机组转换预览:")
        print(f"电站数量: {preview.get('station_count', 0)}")
        print(f"源文件记录数: {preview.get('source_records', 0)}")
        
        print("\n预览生成的机组（前10个）:")
        for i, unit in enumerate(preview.get('preview_units', [])[:10], 1):
            print(f"  {i:2d}. {unit['station_name']} -> {unit['unit_name']}")
            print(f"      项目: {unit['project_name']}")
            print(f"      容量: {unit['unit_capacity']} MW")
            print(f"      类型: {unit['unit_type']}")
            print()
        
        total_units = len(preview.get('preview_units', []))
        if total_units > 10:
            print(f"  ... 还有 {total_units - 10} 个机组")
        
        print("=" * 60)
        
        # 询问用户是否继续
        user_input = input("\n是否继续执行转换？(y/n): ").strip().lower()
        if user_input not in ['y', 'yes', '是']:
            print("转换已取消。")
            return False
        
        # 执行转换
        print("\n开始执行转换...")
        logger.info("开始执行机组数据转换...")
        
        success = converter.convert_stations_to_units()
        
        if success:
            logger.info("🎉 机组数据转换成功完成！")
            print("\n✅ 转换成功完成！")
            print(f"✅ 已生成机组记录")
            print(f"✅ 数据已写入目标文件的'机组表'工作表")
            print(f"✅ 备份文件已自动创建")
            return True
        else:
            logger.error("❌ 机组数据转换失败")
            print("\n❌ 转换失败，请检查日志文件。")
            return False
            
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        print(f"\n❌ 程序执行出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        logger.info("程序结束")

if __name__ == "__main__":
    main()
