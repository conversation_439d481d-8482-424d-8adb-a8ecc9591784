"""
简单的设置测试脚本
"""
import sys
import os
from pathlib import Path

print("Python版本:", sys.version)
print("当前工作目录:", os.getcwd())
print("项目根目录:", Path(__file__).parent)

# 测试导入
try:
    print("\n测试导入模块...")
    
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    from utils.logger import init_main_logger
    print("✓ 成功导入 utils.logger")
    
    from config.settings import FILES
    print("✓ 成功导入 config.settings")
    
    from core.database.connection import db_manager
    print("✓ 成功导入 core.database.connection")
    
    print("\n所有基础模块导入成功！")
    
    # 测试日志初始化
    logger = init_main_logger()
    logger.info("日志系统初始化成功")
    print("✓ 日志系统工作正常")
    
    # 测试数据库初始化
    db_manager.initialize_database()
    print("✓ 数据库初始化成功")
    
    print("\n🎉 基础架构设置完成！")
    
except Exception as e:
    print(f"✗ 导入失败: {str(e)}")
    import traceback
    traceback.print_exc()
