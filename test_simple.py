"""
简单测试修复后的程序
"""
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_import():
    """测试导入"""
    try:
        from src.core.excel_validator import ExcelValidator
        print("✓ 成功导入ExcelValidator")
        
        # 测试文件访问检查方法
        class MockValidator:
            def __init__(self):
                self.file_path = "test.txt"
                
            def _check_file_access(self):
                """测试文件访问检查"""
                try:
                    # 创建一个测试文件
                    with open(self.file_path, 'w') as f:
                        f.write("test")
                    
                    # 尝试以读写模式打开
                    with open(self.file_path, 'r+b'):
                        pass
                    
                    # 清理测试文件
                    os.remove(self.file_path)
                    return True
                except Exception as e:
                    print(f"文件访问测试失败: {str(e)}")
                    return False
        
        mock = MockValidator()
        if mock._check_file_access():
            print("✓ 文件访问检查方法正常工作")
        else:
            print("✗ 文件访问检查方法有问题")
            
        print("✓ 所有基本测试通过")
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_import()
