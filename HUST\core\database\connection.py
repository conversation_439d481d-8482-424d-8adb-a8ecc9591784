"""
数据库连接管理模块
"""
import sqlite3
import os
import logging
from contextlib import contextmanager
from typing import Optional

logger = logging.getLogger(__name__)

class DatabaseConnection:
    """数据库连接管理器"""
    
    def __init__(self, db_path: str = "hust_conversion.db"):
        """
        初始化数据库连接
        
        Args:
            db_path (str): 数据库文件路径
        """
        self.db_path = db_path
        self._connection: Optional[sqlite3.Connection] = None
        
    def connect(self) -> sqlite3.Connection:
        """
        建立数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        try:
            self._connection = sqlite3.connect(self.db_path)
            self._connection.row_factory = sqlite3.Row  # 使结果可以按列名访问
            logger.info(f"成功连接到数据库: {self.db_path}")
            return self._connection
        except Exception as e:
            logger.error(f"连接数据库失败: {str(e)}")
            raise
    
    def disconnect(self):
        """关闭数据库连接"""
        if self._connection:
            self._connection.close()
            self._connection = None
            logger.info("数据库连接已关闭")
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        
        Yields:
            sqlite3.Connection: 数据库连接对象
        """
        conn = None
        try:
            conn = self.connect()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_script(self, script_path: str):
        """
        执行SQL脚本文件
        
        Args:
            script_path (str): SQL脚本文件路径
        """
        if not os.path.exists(script_path):
            raise FileNotFoundError(f"SQL脚本文件不存在: {script_path}")
        
        with open(script_path, 'r', encoding='utf-8') as f:
            script = f.read()
        
        with self.get_connection() as conn:
            conn.executescript(script)
            conn.commit()
            logger.info(f"成功执行SQL脚本: {script_path}")
    
    def initialize_database(self):
        """初始化数据库表结构"""
        init_sql = """
        -- 源数据表
        CREATE TABLE IF NOT EXISTS source_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            raw_data TEXT NOT NULL,
            sheet_name TEXT NOT NULL,
            row_number INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 转换规则表
        CREATE TABLE IF NOT EXISTS conversion_rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            rule_name TEXT NOT NULL UNIQUE,
            source_field TEXT NOT NULL,
            target_field TEXT NOT NULL,
            conversion_type TEXT NOT NULL,
            conversion_params TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 验证规则表
        CREATE TABLE IF NOT EXISTS validation_rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            rule_name TEXT NOT NULL UNIQUE,
            field_name TEXT NOT NULL,
            rule_type TEXT NOT NULL,
            rule_params TEXT,
            error_message TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 转换日志表
        CREATE TABLE IF NOT EXISTS conversion_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            operation_type TEXT NOT NULL,
            source_file TEXT,
            target_file TEXT,
            backup_file TEXT,
            status TEXT NOT NULL,
            error_message TEXT,
            records_processed INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_source_data_sheet ON source_data(sheet_name);
        CREATE INDEX IF NOT EXISTS idx_conversion_rules_active ON conversion_rules(is_active);
        CREATE INDEX IF NOT EXISTS idx_validation_rules_active ON validation_rules(is_active);
        CREATE INDEX IF NOT EXISTS idx_conversion_logs_status ON conversion_logs(status);
        """
        
        with self.get_connection() as conn:
            conn.executescript(init_sql)
            conn.commit()
            logger.info("数据库表结构初始化完成")

# 全局数据库连接实例
db_manager = DatabaseConnection()
