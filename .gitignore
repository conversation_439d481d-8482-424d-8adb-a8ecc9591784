# Python virtual environments
.venv/
venv/
env/
__pycache__/
HUST/backups/
backups/
*.py[cod]
*$py.class

# IDE files
.idea/
.vscode/

# Large Excel files (optional, uncomment if you don't want to track these)
# *.xlsx
# *.xls
01 电源明细表-模板 - 副本.xlsx
01 电源明细表-模板.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906.xls
240923-GOPT_GD_YX-V245-十五五2030-20240906.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本.xls
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本.xlsx
~$240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本 - 副本.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本 - 副本.xlsx
删除机组报价.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本 - 副本.xlsx
240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本 - 副本 - 副本.xlsx
删除机组报价 - 副本.xlsx
HUST/电源明细表手动转HUST.xlsx
HUST/backups/电源明细表手动转HUST_backup_20250611_152515_电站名称转换.xlsx
HUST/~$电源明细表手动转HUST.xlsx
hust_conversion.db
HUST/hust_conversion.log
【最后版本】运行模拟数据20250417(1).xls
node_modules/
standard_library/frontend/node_modules/
standard_library/output/HUST_power_plant_config_20250625_101239.xlsx
standard_library/发电公司_sample.csv
standard_library/风电备用_sample.csv
standard_library/风电场_sample.csv
standard_library/风区信息_sample.csv
standard_library/风区之间相关系数_sample.csv
standard_library/负荷曲线_sample.csv
standard_library/光伏电站_sample.csv
standard_library/光区信息_sample.csv
standard_library/光区之间相关系数_sample.csv
standard_library/机组报价_sample.csv
standard_library/机组规划类型_sample.csv
standard_library/机组指定出力_sample.csv
standard_library/机组指定状态_sample.csv
standard_library/节点_sample.csv
standard_library/水电三段式出力_sample.csv
standard_library/系统_sample.csv
standard_library/线路_sample.csv
standard_library/线路区域_sample.csv
