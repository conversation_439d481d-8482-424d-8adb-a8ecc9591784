"""
分析HUST文件夹中的Excel文件结构
"""
import pandas as pd
import os

def analyze_excel_structure():
    """分析Excel文件结构"""
    
    print('=== 源文件结构分析 ===')
    source_file = '01 电源明细表-模板.xlsx'
    
    try:
        # 读取源文件
        wb_source = pd.ExcelFile(source_file)
        print(f'源文件: {source_file}')
        print(f'工作表: {wb_source.sheet_names}')
        
        # 分析每个工作表
        for sheet_name in wb_source.sheet_names:
            print(f'\n--- {sheet_name} ---')
            try:
                df = pd.read_excel(source_file, sheet_name=sheet_name, nrows=3)
                print(f'行数: {len(pd.read_excel(source_file, sheet_name=sheet_name))}')
                print(f'列数: {len(df.columns)}')
                print('前几列:')
                for i, col in enumerate(df.columns[:10]):  # 只显示前10列
                    print(f'  {i+1}. {col}')
                if len(df.columns) > 10:
                    print(f'  ... 还有{len(df.columns)-10}列')
            except Exception as e:
                print(f'  读取失败: {e}')
                
    except Exception as e:
        print(f'读取源文件失败: {e}')
    
    print('\n=== 目标文件结构分析 ===')
    target_file = '电源明细表手动转HUST.xlsx'
    
    try:
        # 读取目标文件
        wb_target = pd.ExcelFile(target_file)
        print(f'目标文件: {target_file}')
        print(f'工作表: {wb_target.sheet_names}')
        
        # 分析每个工作表
        for sheet_name in wb_target.sheet_names:
            print(f'\n--- {sheet_name} ---')
            try:
                df = pd.read_excel(target_file, sheet_name=sheet_name, nrows=3)
                print(f'行数: {len(pd.read_excel(target_file, sheet_name=sheet_name))}')
                print(f'列数: {len(df.columns)}')
                print('前几列:')
                for i, col in enumerate(df.columns[:10]):  # 只显示前10列
                    print(f'  {i+1}. {col}')
                if len(df.columns) > 10:
                    print(f'  ... 还有{len(df.columns)-10}列')
            except Exception as e:
                print(f'  读取失败: {e}')
                
    except Exception as e:
        print(f'读取目标文件失败: {e}')

if __name__ == "__main__":
    analyze_excel_structure()
