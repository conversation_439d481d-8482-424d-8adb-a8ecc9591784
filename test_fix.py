"""
测试修复后的程序
"""
import sys
import os
import traceback

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_excel_validator():
    """测试Excel验证器"""
    try:
        from src.core.excel_validator import ExcelValidator
        print("✓ 成功导入ExcelValidator")
        
        # 检查是否有Excel文件可以测试
        test_files = [
            "01 电源明细表-模板.xlsx",
            "240923-GOPT_GD_YX-V245-十五五2030-20240906 - 副本.xlsx"
        ]
        
        test_file = None
        for file in test_files:
            if os.path.exists(file):
                test_file = file
                break
        
        if test_file:
            print(f"✓ 找到测试文件: {test_file}")
            
            # 创建验证器实例
            validator = ExcelValidator(test_file)
            print("✓ 成功创建验证器实例")
            
            # 测试加载数据
            if validator.load_data():
                print("✓ 成功加载数据")
            else:
                print("✗ 加载数据失败")
                
            # 测试文件访问检查
            if validator._check_file_access():
                print("✓ 文件访问检查通过")
            else:
                print("✗ 文件访问检查失败")
                
        else:
            print("✗ 未找到测试文件")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_excel_validator()
