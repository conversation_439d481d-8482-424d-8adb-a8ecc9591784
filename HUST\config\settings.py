"""
系统配置文件
"""
import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

# 文件路径配置
FILES = {
    'source_file': '01 电源明细表-模板.xlsx',
    'target_file': '电源明细表手动转HUST.xlsx',
    'database_file': 'hust_conversion.db',
    'log_file': 'hust_conversion.log'
}

# 数据库配置
DATABASE = {
    'path': PROJECT_ROOT / FILES['database_file'],
    'timeout': 30,
    'check_same_thread': False
}

# Excel配置
EXCEL = {
    'engine': 'openpyxl',
    'na_filter': False,
    'keep_default_na': False,
    'read_only': False,
    'data_only': True
}

# 日志配置
LOGGING = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file_path': PROJECT_ROOT / FILES['log_file'],
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# 备份配置
BACKUP = {
    'directory': PROJECT_ROOT / 'backups',
    'max_count': 50,
    'retention_days': 30,
    'auto_cleanup': True
}

# GUI配置
GUI = {
    'window_size': '800x600',
    'window_title': 'HUST数据转换系统',
    'theme': 'default',
    'font_family': 'Arial',
    'font_size': 10
}

# 验证配置
VALIDATION = {
    'strict_mode': False,
    'stop_on_first_error': False,
    'max_errors': 100
}

# 转换配置
CONVERSION = {
    'batch_size': 1000,
    'enable_progress': True,
    'auto_save': True,
    'save_interval': 100  # 每处理100行保存一次
}

# 工作表配置
WORKSHEETS = {
    'source_sheets': [
        'Sheet1'  # 根据实际情况调整
    ],
    'target_sheets': [
        'Sheet1'  # 根据实际情况调整
    ]
}

# 字段类型配置
FIELD_TYPES = {
    'text': str,
    'number': float,
    'integer': int,
    'boolean': bool,
    'date': 'datetime',
    'time': 'time'
}

# 错误处理配置
ERROR_HANDLING = {
    'continue_on_error': True,
    'log_errors': True,
    'show_error_dialog': True,
    'create_error_report': True
}

# 性能配置
PERFORMANCE = {
    'use_multiprocessing': False,
    'max_workers': 4,
    'chunk_size': 1000,
    'memory_limit_mb': 512
}

def get_file_path(file_key: str) -> Path:
    """
    获取文件的完整路径

    Args:
        file_key (str): 文件键名

    Returns:
        Path: 文件路径
    """
    if file_key in FILES:
        return PROJECT_ROOT / FILES[file_key]
    else:
        raise KeyError(f"未知的文件键: {file_key}")

def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        BACKUP['directory'],
        PROJECT_ROOT / 'logs',
        PROJECT_ROOT / 'temp'
    ]

    for directory in directories:
        directory.mkdir(exist_ok=True)

# 初始化时创建必要目录
ensure_directories()
