"""
电站转换执行脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.logger import init_main_logger, get_main_logger
from processors.station_converter import StationConverter
from config.settings import FILES

def main():
    """主函数"""
    # 初始化日志
    logger = init_main_logger(
        log_file="station_conversion.log",
        level="INFO"
    )
    
    logger.info("=" * 60)
    logger.info("电站数据转换程序启动")
    logger.info("=" * 60)
    
    try:
        # 文件路径
        source_file = project_root / FILES['source_file']
        target_file = project_root / FILES['target_file']
        
        logger.info(f"源文件: {source_file}")
        logger.info(f"目标文件: {target_file}")
        
        # 检查文件是否存在
        if not source_file.exists():
            logger.error(f"源文件不存在: {source_file}")
            return False
        
        if not target_file.exists():
            logger.error(f"目标文件不存在: {target_file}")
            return False
        
        # 创建转换器
        converter = StationConverter(str(source_file), str(target_file))
        
        # 获取转换预览
        logger.info("获取转换预览...")
        preview = converter.get_conversion_preview()
        
        if preview:
            logger.info("=" * 40)
            logger.info("转换预览:")
            logger.info(f"源文件总记录数: {preview.get('total_source_records', 0)}")
            logger.info(f"符合条件的记录数: {preview.get('filtered_records', 0)}")
            logger.info(f"唯一项目数: {preview.get('unique_projects', 0)}")
            logger.info(f"目标机组类型: {preview.get('target_machine_types', [])}")
            
            logger.info("\n预览生成的电站名称:")
            for i, station in enumerate(preview.get('preview_stations', []), 1):
                logger.info(f"  {i}. {station['original_project']} -> {station['generated_station']}")
            
            if preview.get('has_more', False):
                logger.info(f"  ... 还有更多项目")
            
            logger.info("=" * 40)
        
        # 询问用户是否继续
        print("\n转换预览已显示在日志中。")
        print("请检查日志文件确认转换内容。")
        
        user_input = input("\n是否继续执行转换？(y/N): ").strip().lower()
        
        if user_input in ['y', 'yes']:
            logger.info("用户确认执行转换")
            
            # 执行转换
            logger.info("开始执行电站数据转换...")
            success = converter.convert_projects_to_stations()
            
            if success:
                logger.info("🎉 电站数据转换成功完成！")
                print("\n✓ 转换成功完成！请检查目标文件。")
                return True
            else:
                logger.error("❌ 电站数据转换失败")
                print("\n✗ 转换失败，请检查日志文件。")
                return False
        else:
            logger.info("用户取消转换")
            print("转换已取消。")
            return True
            
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        print(f"\n程序执行出错: {str(e)}")
        return False
    
    finally:
        logger.info("程序结束")

def preview_only():
    """仅预览模式"""
    # 初始化日志
    logger = init_main_logger(level="INFO")
    
    try:
        source_file = project_root / FILES['source_file']
        target_file = project_root / FILES['target_file']
        
        converter = StationConverter(str(source_file), str(target_file))
        preview = converter.get_conversion_preview()
        
        print("=" * 60)
        print("电站转换预览")
        print("=" * 60)
        print(f"源文件总记录数: {preview.get('total_source_records', 0)}")
        print(f"符合条件的记录数: {preview.get('filtered_records', 0)}")
        print(f"唯一项目数: {preview.get('unique_projects', 0)}")
        print(f"目标机组类型: {', '.join(preview.get('target_machine_types', []))}")
        
        print("\n预览生成的电站名称:")
        for i, station in enumerate(preview.get('preview_stations', []), 1):
            print(f"  {i:2d}. {station['original_project']} -> {station['generated_station']}")
        
        if preview.get('has_more', False):
            total = preview.get('unique_projects', 0)
            shown = len(preview.get('preview_stations', []))
            print(f"  ... 还有 {total - shown} 个项目")
        
        print("=" * 60)
        
    except Exception as e:
        print(f"预览失败: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "preview":
        preview_only()
    else:
        main()
