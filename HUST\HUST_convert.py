import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
from openpyxl import load_workbook
from openpyxl.styles import Alignment
import os
import re


def update_excel_station_with_format(dataframe, excel_path, sheet_name, key_column='id', center_alignment=True):
    """
    更新已有Excel文件中特定sheet的数据，保留原有格式（如颜色、样式等）

    参数:
    dataframe (pd.DataFrame): 包含新数据的DataFrame
    excel_path (str): 已有Excel文件的路径
    sheet_name (str): 要更新的sheet名称
    key_column (str): 用于匹配行的键列名称
    center_alignment (bool): 是否设置单元格居中对齐

    返回:
    dict: 项目名称与电站ID的映射关系
    """
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"指定的Excel文件不存在: {excel_path}")

    # 加载工作簿
    wb = load_workbook(excel_path)

    # 检查sheet是否存在
    if sheet_name not in wb.sheetnames:
        raise ValueError(f"Excel文件中不存在名为 '{sheet_name}' 的sheet")

    # 获取指定sheet
    ws = wb[sheet_name]

    # 获取DataFrame的列名
    df_columns = dataframe.columns.tolist()

    # 获取Excel表头行（假设表头在第1行）
    header = [cell.value for cell in ws[1]]

    # 确保表头至少有一列
    if not header:
        header = [key_column]
        ws.cell(row=1, column=1).value = key_column

    # 获取键列在Excel中的列索引（从1开始）
    if key_column in header:
        key_col_idx = header.index(key_column) + 1
    else:
        # 如果Excel中不存在键列，添加该键列
        key_col_idx = len(header) + 1
        ws.cell(row=1, column=key_col_idx).value = key_column
        header.append(key_column)

    # 处理电站ID列逻辑
    power_station_id_col = '电站ID'
    if power_station_id_col not in header:
        # 如果不存在电站ID列，添加到表头
        power_station_id_idx = len(header) + 1
        ws.cell(row=1, column=power_station_id_idx).value = power_station_id_col
        header.append(power_station_id_col)
    else:
        # 如果存在，获取其列索引
        power_station_id_idx = header.index(power_station_id_col) + 1

    # 获取当前最大电站ID值
    max_id = 0
    if ws.max_row > 1:  # 确保有数据行
        # 遍历所有数据行，查找最大电站ID
        for row_idx in range(2, ws.max_row + 1):
            id_value = ws.cell(row=row_idx, column=power_station_id_idx).value
            # 确保ID值是整数类型并更新最大值
            if id_value and isinstance(id_value, int) and id_value > max_id:
                max_id = id_value

    # 创建一个字典，映射键值到Excel中的行号
    key_to_row = {}
    for row_idx in range(2, ws.max_row + 1):  # 从第2行开始（跳过表头）
        key_value = ws.cell(row=row_idx, column=key_col_idx).value
        if key_value is not None:
            key_to_row[key_value] = row_idx

    # 设置居中对齐样式
    if center_alignment:
        alignment = Alignment(horizontal='center', vertical='center')

    # 创建项目名称与电站ID的映射字典
    name_to_id = {}

    # 遍历DataFrame中的每一行数据
    for _, row_data in dataframe.iterrows():
        # 如果DataFrame中不存在键列，使用行索引作为键值
        key_value = row_data.get(key_column, _)

        # 提取原始项目名称（从第一个中文字符开始）
        original_name = extract_chinese_text(str(key_value))

        # 如果键值存在于Excel中，则更新该行数据
        if key_value in key_to_row:
            row_idx = key_to_row[key_value]
            # 获取现有行的电站ID
            existing_id = ws.cell(row=row_idx, column=power_station_id_idx).value
            # 更新映射字典
            name_to_id[original_name] = existing_id

            # 更新每一列的数据
            for col_name in df_columns:
                if col_name in header:
                    col_idx = header.index(col_name) + 1
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.value = row_data[col_name]
                    # 设置居中对齐
                    if center_alignment:
                        cell.alignment = alignment
        else:
            # 如果键值不存在，则在Excel中新增一行
            new_row_idx = ws.max_row + 1
            max_id += 1  # 新增行的电站ID递增

            # 设置电站ID单元格并居中对齐
            id_cell = ws.cell(row=new_row_idx, column=power_station_id_idx)
            id_cell.value = max_id
            if center_alignment:
                id_cell.alignment = alignment

            # 更新映射字典
            name_to_id[original_name] = max_id

            # 填充新行的其他数据
            for col_name in df_columns:
                if col_name in header:
                    col_idx = header.index(col_name) + 1
                    cell = ws.cell(row=new_row_idx, column=col_idx)
                    cell.value = row_data[col_name]
                    # 设置居中对齐
                    if center_alignment:
                        cell.alignment = alignment

    # 保存工作簿
    wb.save(excel_path)
    print(f"成功更新Excel文件 '{excel_path}' 的sheet '{sheet_name}'，保留原有格式")

    # 返回项目名称与电站ID的映射字典
    return name_to_id


def update_excel_unit_with_format(dataframe, excel_path, sheet_name, key_column='名称', center_alignment=True):
    """
    更新已有Excel文件中特定sheet的数据，保留原有格式（如颜色、样式等），处理机组ID

    参数:
    dataframe (pd.DataFrame): 包含新数据的DataFrame
    excel_path (str): 已有Excel文件的路径
    sheet_name (str): 要更新的sheet名称
    key_column (str): 用于匹配行的键列名称
    center_alignment (bool): 是否设置单元格居中对齐

    返回:
    None
    """
    # 检查文件是否存在
    if not os.path.exists(excel_path):
        raise FileNotFoundError(f"指定的Excel文件不存在: {excel_path}")

    # 加载工作簿
    wb = load_workbook(excel_path)

    # 检查sheet是否存在
    if sheet_name not in wb.sheetnames:
        raise ValueError(f"Excel文件中不存在名为 '{sheet_name}' 的sheet")

    # 获取指定sheet
    ws = wb[sheet_name]

    # 获取DataFrame的列名
    df_columns = dataframe.columns.tolist()

    # 获取Excel表头行（假设表头在第1行）
    header = [cell.value for cell in ws[1]]

    # 确保表头至少有一列
    if not header:
        header = [key_column]
        ws.cell(row=1, column=1).value = key_column

    # 获取键列在Excel中的列索引（从1开始）
    if key_column in header:
        key_col_idx = header.index(key_column) + 1
    else:
        # 如果Excel中不存在键列，添加该键列
        key_col_idx = len(header) + 1
        ws.cell(row=1, column=key_col_idx).value = key_column
        header.append(key_column)

    # 处理机组ID列逻辑
    unit_id_col = '机组ID'
    if unit_id_col not in header:
        # 如果不存在机组ID列，添加到表头
        unit_id_idx = len(header) + 1
        ws.cell(row=1, column=unit_id_idx).value = unit_id_col
        header.append(unit_id_col)
    else:
        # 如果存在，获取其列索引
        unit_id_idx = header.index(unit_id_col) + 1

    # 获取当前最大机组ID值
    max_id = 0
    if ws.max_row > 1:  # 确保有数据行
        # 遍历所有数据行，查找最大机组ID
        for row_idx in range(2, ws.max_row + 1):
            id_value = ws.cell(row=row_idx, column=unit_id_idx).value
            # 确保ID值是整数类型并更新最大值
            if id_value and isinstance(id_value, int) and id_value > max_id:
                max_id = id_value

    # 创建一个字典，映射键值到Excel中的行号
    key_to_row = {}
    for row_idx in range(2, ws.max_row + 1):  # 从第2行开始（跳过表头）
        key_value = ws.cell(row=row_idx, column=key_col_idx).value
        if key_value is not None:
            key_to_row[key_value] = row_idx

    # 设置居中对齐样式
    if center_alignment:
        alignment = Alignment(horizontal='center', vertical='center')

    # 遍历DataFrame中的每一行数据
    for _, row_data in dataframe.iterrows():
        # 如果DataFrame中不存在键列，使用行索引作为键值
        key_value = row_data.get(key_column, _)

        # 如果键值存在于Excel中，则更新该行数据
        if key_value in key_to_row:
            row_idx = key_to_row[key_value]
            # 获取现有行的机组ID
            existing_id = ws.cell(row=row_idx, column=unit_id_idx).value

            # 更新每一列的数据
            for col_name in df_columns:
                if col_name in header:
                    col_idx = header.index(col_name) + 1
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.value = row_data[col_name]
                    # 设置居中对齐
                    if center_alignment:
                        cell.alignment = alignment
        else:
            # 如果键值不存在，则在Excel中新增一行
            new_row_idx = ws.max_row + 1
            max_id += 1  # 新增行的机组ID递增

            # 设置机组ID单元格并居中对齐
            id_cell = ws.cell(row=new_row_idx, column=unit_id_idx)
            id_cell.value = max_id
            if center_alignment:
                id_cell.alignment = alignment

            # 填充新行的其他数据
            for col_name in df_columns:
                if col_name in header:
                    col_idx = header.index(col_name) + 1
                    cell = ws.cell(row=new_row_idx, column=col_idx)
                    cell.value = row_data[col_name]
                    # 设置居中对齐
                    if center_alignment:
                        cell.alignment = alignment

    # 保存工作簿
    wb.save(excel_path)
    print(f"成功更新Excel文件 '{excel_path}' 的sheet '{sheet_name}'，保留原有格式")

def extract_chinese_text(text):
    """
    提取字符串中从第一个中文字符开始的所有内容

    参数:
    text (str): 需要处理的字符串

    返回:
    str: 提取的中文文本，如果没有找到中文字符则返回空字符串
    """
    # 使用正则表达式匹配第一个中文字符的位置
    match = re.search(r'[\u4e00-\u9fff]', text)

    # 如果找到中文字符，返回从该位置开始的所有内容
    if match:
        return text[match.start():]
    else:
        return ''  # 如果没有找到中文字符，返回空字符串

def extract_mapping(names):
    """
    从给定的名称列表中提取前缀和主体部分，构建映射字典

    参数:
    names (list): 包含形如"QD_1_9F_昭阳A厂"的名称列表

    返回:
    dict: 映射字典，键为前缀部分，值为主体部分
    """
    mapping = {}
    # 定义更灵活的正则表达式模式
    #pattern = r'^([A-Z]+_\d+_[A-Z0-9]+)_(.*)$'
    pattern = r'^([^_]+_[^_]+_[^_]+)_(.*)$'

    for name in names:
        # 使用正则表达式进行匹配
        match = re.match(pattern, name)
        if match:
            prefix = match.group(1)  # 前缀部分，如"QD_1_9F"
            main_part = match.group(2)  # 主体部分，如"昭阳A厂"
            # 添加到映射字典中
            mapping[prefix] = main_part
        else:
            print(f"无法解析名称: {name}")

    return mapping

def main():
    # 创建GUI窗口
    root = tk.Tk()
    root.title("Excel数据处理工具")
    root.geometry("500x400")  # 调整窗口高度
    root.configure(bg="#f0f0f0")

    # 设置字体
    label_font = ("SimHei", 10)
    entry_font = ("SimHei", 10)
    button_font = ("SimHei", 10, "bold")

    # 创建变量存储输入值
    file_path = tk.StringVar()
    type_name = tk.StringVar(value="QD_9F")
    sheet_name = tk.StringVar(value="电站表")  # sheet名称变量
    save_existing = tk.BooleanVar(value=False)  # 是否保存到现有文件
    center_alignment = tk.BooleanVar(value=True)  # 是否居中对齐

    # 选择文件函数
    def browse_file():
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        file_path.set(filename)

    # 处理数据函数
    def process_data():
        try:
            # 获取输入值
            path = file_path.get()
            type_val = type_name.get()
            sheet_val = sheet_name.get()
            save_to_existing = save_existing.get()
            center_aligned = center_alignment.get()

            if not path or not type_val:
                messagebox.showerror("错误", "请输入文件路径和机组类型")
                return

            # 读取数据
            data = pd.read_excel(path, engine='openpyxl')

            # 筛选特定机组类型的数据
            station_data = station_type_convert(data, type_val)

            # 添加新列并设置默认值
            station_cols_name = ['有效性', '节点ID', '类型', '检修场地', '备用Rmax',
                         '储能比率', '储能效率', '储能损耗', '期望电量',
                         '最小电量', '电站约束', '流域ID', '优化空间']
            station_data = station_get_cols(station_data, station_cols_name)

            # 根据用户选择保存数据
            if save_to_existing:
                # 让用户选择目标文件路径
                save_path = filedialog.askopenfilename(
                    title="选择要保存到的Excel文件",
                    filetypes=[("Excel files", "*.xlsx *.xls")]
                )

                if not save_path:
                    return  # 用户取消了操作

                # 使用保留格式的方法保存到选择的文件
                name_id_mapping = update_excel_station_with_format(
                    station_data,
                    save_path,
                    sheet_val,
                    key_column='名称',  # 使用项目名称作为键列
                    center_alignment=center_aligned
                )

                messagebox.showinfo("成功", f"数据已成功添加到 {save_path} 的 {sheet_val} 工作表，保留原有格式")
            else:
                # 保存为新文件
                save_path = filedialog.asksaveasfilename(
                    title="保存文件",
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx")]
                )

                if save_path:
                    # 为新文件生成连续的电站ID
                    if not station_data.empty:
                        station_data['电站ID'] = range(1, len(station_data) + 1)

                    # 创建项目名称与电站ID的映射
                    name_id_mapping = {}
                    for index, row in station_data.iterrows():
                        # 提取原始项目名称
                        original_name = extract_chinese_text(row['名称'])
                        name_id_mapping[original_name] = row['电站ID']

                    station_data.to_excel(save_path, sheet_name=sheet_val, index=False)
                    messagebox.showinfo("成功", f"数据已成功保存到 {save_path}")

            # # 输出电站ID映射字典
            # print("\n项目名称与电站ID的映射关系:")
            # for name, id in name_id_mapping.items():
            #     print(f"{name}: {id}")

            unit_data = unit_type_convert(data, type_val, name_id_mapping)
            # 添加新列并设置默认值
            # 电站ID和名称已有
            unit_cols_name = ['有效性', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                         '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                         '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                         '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']
            unit_data = unit_get_cols(unit_data, unit_cols_name)

            # 让用户选择保存机组数据的文件
            unit_file_path = filedialog.askopenfilename(
                title="选择要保存机组数据的Excel文件",
                filetypes=[("Excel files", "*.xlsx *.xls")]
            )

            if unit_file_path:
                update_excel_unit_with_format(
                    unit_data,
                    unit_file_path,
                    '机组表',  # 固定使用"机组表"作为sheet名称
                    key_column='名称',  # 使用项目名称作为键列
                    center_alignment=center_aligned
                )
                messagebox.showinfo("成功", f"机组数据已成功保存到 {unit_file_path} 的 机组表 工作表")
            root.destroy()  # 处理完成后关闭窗口

        except Exception as e:
            messagebox.showerror("处理错误", f"发生错误: {str(e)}")

    # 创建界面元素
    frame = tk.Frame(root, bg="#f0f0f0", padx=20, pady=20)
    frame.pack(fill=tk.BOTH, expand=True)

    # 文件路径选择
    tk.Label(frame, text="Excel文件路径:", font=label_font, bg="#f0f0f0").grid(row=0, column=0, sticky=tk.W, pady=10)
    tk.Entry(frame, textvariable=file_path, width=40, font=entry_font).grid(row=0, column=1, pady=10, padx=5)
    tk.Button(frame, text="浏览...", command=browse_file, font=button_font, bg="#4CAF50", fg="white").grid(row=0,
                                                                                                         column=2,
                                                                                                         pady=10,
                                                                                                         padx=5)

    # 机组类型输入
    tk.Label(frame, text="机组类型:", font=label_font, bg="#f0f0f0").grid(row=1, column=0, sticky=tk.W, pady=10)
    tk.Entry(frame, textvariable=type_name, width=40, font=entry_font).grid(row=1, column=1, pady=10, padx=5)

    # sheet名称输入
    tk.Label(frame, text="Sheet名称:", font=label_font, bg="#f0f0f0").grid(row=2, column=0, sticky=tk.W, pady=10)
    tk.Entry(frame, textvariable=sheet_name, width=40, font=entry_font).grid(row=2, column=1, pady=10, padx=5)

    # 是否保存到现有文件的选项
    tk.Checkbutton(frame, text="保存到现有文件（保留其他sheet）", variable=save_existing, font=label_font, bg="#f0f0f0").grid(row=3,
                                                                                                                 column=0,
                                                                                                                 columnspan=3,
                                                                                                                 sticky=tk.W,
                                                                                                                 pady=10)

    # 是否居中对齐的选项
    tk.Checkbutton(frame, text="设置单元格居中对齐", variable=center_alignment, font=label_font, bg="#f0f0f0").grid(row=4,
                                                                                                           column=0,
                                                                                                           columnspan=3,
                                                                                                           sticky=tk.W,
                                                                                                           pady=10)

    # 处理按钮
    tk.Button(frame, text="开始处理", command=process_data, font=button_font, bg="#2196F3", fg="white", height=2,
              width=15).grid(row=5, column=0, columnspan=3, pady=20)

    # 版权信息
    tk.Label(root, text="Excel数据处理工具 © 2025", font=("SimHei", 9), bg="#f0f0f0", fg="#666666").pack(side=tk.BOTTOM,
                                                                                                   pady=10)

    # 运行主循环
    root.mainloop()


def station_type_convert(data, type_name):
    # 选择“机组类型”为type_name的行
    # 电源明细表 -> 电站表
    data = data[(data['机组类型'] == type_name)]
    data = data[data.机组类型.isin([type_name])]
    data = pd.DataFrame(data, columns=['项目名称', '机组类型'])
    data.drop_duplicates(subset='项目名称', keep='last', inplace=True, ignore_index=False)

    # 修改索引
    data = data.reset_index(drop=True)

    # “机组类型”添加到“项目名称”前置
    type_name = type_name + '_'
    for i in range(len(data)):
        data['项目名称'][i] = type_name[:3] + str(i + 1) + type_name[2:6] + data['项目名称'][i]

    # 只保留“项目名称”列重命名
    data = pd.DataFrame(data['项目名称'])
    data.rename(columns={'项目名称': '名称'}, inplace=True)
    return data


def unit_type_convert(data, type_name, name_id_mapping):
    # 选择“机组类型”为type_name的行
    # 电源明细表 -> 机组表
    data = data[(data['机组类型'] == type_name)]
    data = data[data.机组类型.isin([type_name])]
    # 修改索引
    data = data.reset_index(drop=True)
    # 根据"名称"对应字典找到"电站ID"
    data['电站ID'] = 0
    for i in range(len(data)):
        data['电站ID'][i] = name_id_mapping[data['项目名称'][i]]
    # “机组类型”添加到“项目名称”前置
    type_name = type_name + '_'
    for i in range(len(data)):
        data['项目名称'][i] = type_name[:3] + str(i + 1) + type_name[2:6] + data['项目名称'][i]
    # “机组序号”添加到“项目名称”后置
    for i in range(len(data)):
        data['项目名称'][i] = data['项目名称'][i] + '_' + str(data['机组序号'][i])
    # 只保留“项目类型”列
    data.rename(columns={'项目名称': '名称'}, inplace=True)
    return data


def station_get_cols(data, cols_name):
    """添加新列并设置默认值"""
    for col_name in cols_name:
        if col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax':
            data[col_name] = 1
        elif col_name == '类型':
            data[col_name] = 320
        else:
            data[col_name] = 0
    return data

def unit_get_cols(data, cols_name):
    """添加新列并设置默认值"""
    # 重命名
    data.rename(columns={'机组容量': '单机容量', '投产时间': '投产年月'}, inplace=True)

    # 赋值
    for col_name in cols_name:
        if col_name == '动态投资':
            data[col_name] = 2700
        elif col_name == '燃料单价':
            data[col_name] = 1654
        elif col_name == '燃料单耗':
            data[col_name] = 220
        elif col_name == '检修天数':
            data[col_name] = 30
        elif col_name == '特性ID':
            data[col_name] = 5
        elif col_name == '有效性' or col_name == '节点ID' or col_name == '检修场地' or col_name == '备用Rmax' or col_name == '台数':
            data[col_name] = 1
        elif col_name == '上网电价' or col_name == '汛期电价':
            data[col_name] = 0.585
        elif col_name == '技术出力':
            data[col_name] = 0
        elif col_name == '爬坡率' or col_name == '强迫停运':
            data[col_name] = 0.05
        elif col_name == '运维费率':
            data[col_name] = 0.04
        elif col_name == '运行费':
            data[col_name] = 0.02
        elif col_name == '类型' or col_name == '储能库容' or col_name == '退役年月' or col_name == '退役进度' or col_name == '变电投资' or col_name == '功频系数' or col_name == '惯性常数' or col_name == '投产进度':
            data[col_name] = 0

    # 处理投产年月列
    # 将所有能转换为日期的元素转换为datetime类型
    data['投产年月'] = pd.to_datetime(data['投产年月'], errors='coerce')

    # 提取年月信息并转换为整数
    year_month_series = data['投产年月'].dt.strftime('%Y%m')

    # 将年月信息更新到投产年月列
    data['投产年月'] = year_month_series

    # 设置投产进度为101的条件
    mask = (data['投产年月'].notna()) & (data['投产年月'] >= '202506')
    data.loc[mask, '投产进度'] = 101

    cols_name = ['名称', '有效性', '电站ID', '单机容量', '台数', '类型', '技术出力', '储能库容', '检修天数',
                 '特性ID', '投产年月', '投产进度', '退役年月', '退役进度', '动态投资',
                 '变电投资', '运维费率', '运行费', '燃料单耗', '燃料单价', '上网电价',
                 '汛期电价', '爬坡率', '功频系数', '惯性常数', '强迫停运']

    return data[cols_name]

if __name__ == '__main__':
    main()